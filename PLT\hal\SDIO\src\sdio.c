/******************************************************************************
*(C) Copyright 2022 ASR Microelectronics
* All Rights Reserved
******************************************************************************/

/***************************************************************************
* Filename: sdio.c
*
* Description:
* SDIO bus driver
* SDIO init and supply basic APIs
*
* Notes:
******************************************************************************/
#include <stdlib.h>
#include "sdio.h"
#include "sdio_api.h"
#include "intc.h"
#include "intc_list.h"
#include "bsp_hisr.h"
#include "platform.h"
#include "csw_mem.h"
#include "gpio.h"
#include "cgpio.h"
#include "guilin.h"
#ifdef QUECTEL_PROJECT_CUST
#include "FDI_EXT.h"
#include "FDI_TYPE.h"
#include "FDI_FILE.h"
#endif

#ifdef QUECTEL_PROJECT_CUST
#define EVB_SDCARD_2_SDIO 1

#define SDIO_DEBUG 0    //LOG ON/OFF

#define ENABLE_SDIO_ADMA		//enable ADMA2
#define SDIO_PHASE 1
 

int wlan_ldo_en_pin = -1;
int wlan_hw_reset_pin = -1;
int host_wake_wlan_pin = -1;
int wlan_wake_host_pin = -1;
#ifdef SSVWIFI_FUNCTION
#define SDIO_CLOCK_ADJ			//pre write: "clock always on" off, after write: "clock always on"
#endif

#ifdef AICWIFI_SUPPORT
#ifdef	PROTECT_TX_ADMA
#define	SDIO_DATA_POLL
#endif
#define SDIO_CLOCK_ADJ			//pre write: "clock always on" off, after write: "clock always on"

#define	CONFIG_ADMA_PROTECT
#define	CONFIG_POLL_TH				136//136/3
static UINT16 amda_seg_cnt = 0;
#endif
#else
#define SDIO_DEBUG 0    //LOG ON/OFF

#define ENABLE_SDIO_ADMA		//enable ADMA2
#ifdef SSVWIFI_FUNCTION
#define SDIO_CLOCK_ADJ			//pre write: "clock always on" off, after write: "clock always on"
#endif
#endif // QUECTEL_PROJECT_CUST




/********************************************************
                Local Variables
********************************************************/
#define     SDIO_INTERRUPT_FLAG_BIT  0x2
#define		SDIO_CARDINT_TASK_STACK_SIZE	2048
static void  * SDIOStack;
OSSemaRef   SDIOSysRef  = NULL;
OSSemaRef   SDIOCmdRef  = NULL;
OSTaskRef   SDIOTaskRef = NULL;
OSFlagRef   SDIOFlgRef  = NULL;

#ifdef QUECTEL_PROJECT_CUST
#define 	SDIO_DATA_TICK	100
#else
#define 	SDIO_DATA_TICK	10
#endif

static OSSemaRef 	sdio_sem_data = NULL;
static OS_HISR   	SdioSemHISR;
static OSATimerRef  DeleteSdioInitTimer = NULL;

OSTaskRef		SDIOInitRef   = NULL;
static void *    SDIOInitStack = NULL;
#define		SDIOINIT_STACK_SIZE		0x2000 /*8K*/
#define		SDIOINIT_TASK_PRIORITY	40

volatile static UINT16 			MMC_RESPONSE[8];         //response registers
volatile static UINT32          CMD_COMP_status = 0;
volatile static UINT32          DATA_COMP_status = 0;

static sdio_cccr                sdiocccr;       //backup cccr for use later
static struct sdio_func 		sdiofunc[MAXFUNCTION];  //contain fn0

static UINT32		MMC_RCA = 0;    //RCA of MMC card
static UINT32		SD_CTRL_BASE_ADDR = 0;
static int			funcnumber = 0;    //sum of functions
static OS_HISR		SdioHISR;   //HISR control block
static UINT8		sdiofnnum = 0;
static UINT16		intstsrec = 0;
static UINT16		errintstsrec = 0;
static UINT8 *		bufaddr = NULL;
volatile static UINT8 cmderror = 0;
static UINT8		catrecerr = 0;
static UINT32       sdiocapa1 = 0;
static UINT32       sdiocapa2 = 0;
static UINT32       sdiocapa3 = 0;
static UINT32       sdiocapa4 = 0;
static UINT32       bkocr = 0;    //back up ocr
static UINT32       sd3_bus_mode = 0;
static UINT32       sd3_drv_type = 0;
volatile static int   sdio_cardint = 0;    //card interrupt flag
static int 			is_sdioclk_48M = 0;		//not support on lapwing
static UINT8 		sdio_wake_lock = 0;
static UINT8        sdio_timing = MMC_TIMING_SD_HS;
static const UINT8 tuning_pattern[] =
{
	0xff, 0x0f, 0xff, 0x00,   0xff, 0xcc, 0xc3, 0xcc,   0xc3, 0x3c, 0xcc, 0xff,   0xfe, 0xff, 0xfe, 0xef,
	0xff, 0xdf, 0xff, 0xdd,   0xff, 0xfb, 0xff, 0xfb,   0xbf, 0xff, 0x7f, 0xff,   0x77, 0xf7, 0xbd, 0xef,
	0xff, 0xf0, 0xff, 0xf0,   0x0f, 0xfc, 0xcc, 0x3c,   0xcc, 0x33, 0xcc, 0xcf,   0xff, 0xef, 0xff, 0xee,
	0xff, 0xfd, 0xff, 0xfd,   0xdf, 0xff, 0xbf, 0xff,   0xbb, 0xff, 0xf7, 0xff,   0xf7, 0x7f, 0x7b, 0xde,
};


#ifdef ENABLE_SDIO_ADMA
#ifdef QUECTEL_PROJECT_CUST
#define SDIO_TX_SLIST_MAX    136
#define SYS_CACHE_LINE_SIZE  32U
#define WCN_CACHE_ALIGN_VARIABLE    __attribute__((aligned(SYS_CACHE_LINE_SIZE)))
#define SDIO_ADMATL_NUM     (136)
#else
#define             SDIO_ADMATL_NUM        (35)
#endif
sdio_desc_table *   sdioadmatl = NULL;
#pragma arm section rwdata="SDIOAdmaTbl", zidata="SDIOAdmaTbl"
unsigned char sdioadmatblnocache[sizeof(sdio_desc_table)*SDIO_ADMATL_NUM];
#ifdef QUECTEL_PROJECT_CUST
WCN_CACHE_ALIGN_VARIABLE unsigned char sdio_tx_buf_fill[512];
WCN_CACHE_ALIGN_VARIABLE unsigned char sdio_tx_buf_dummy[SDIO_TX_SLIST_MAX][8];
#endif
#pragma arm section rwdata, zidata
#endif

#ifndef SSVWIFI_FUNCTION
static int WIFI_PWREN_PIN = -1;
static int WIFI_INT_PIN   = -1;
static int SDIO_WIFI_SLOT = -1;
static int WIFI_RESET_PIN = -1;
static int HOST_WAKEUP_WIFI = -1;
#endif

/********************************************************
        Global Variables
********************************************************/
#ifdef QUECTEL_PROJECT_CUST
#ifdef SSVWIFI_FUNCTION
extern UINT32 DEVICEID; //from moal_init.c
#else
UINT32 DEVICEID = 0;
#endif
#else
extern UINT32 DEVICEID; //from moal_init.c
#endif
UINT32 g_wifi_wake_no_lock = 0;//global flag for at command

/********************************************************
        Local Functions
*******************************************************/
static void sdhci_dumpregs(UINT32 base);
int SDIO_CMD52(int write, unsigned fn, unsigned addr, UINT8 in, UINT8* out);
static int sdio_adma_sg(unsigned fn, int write, unsigned addr, int incr_addr, UINT8 *buf, unsigned listcnt);
static int sdio_adma_data(UINT16 cmd, UINT32 arg, sdio_desc_table* buf, unsigned blocks, unsigned blksz, UINT16 mode);
static void printdesctbl(sdio_desc_table *head);
static void sdio_hisr(void);
static int sdio_host_init(void);
int asr_execute_tuning(UINT32 timing);
#ifndef SSVWIFI_FUNCTION
int platform_5803_get_hw_reset_gpio(void);
#endif

/********************************************************
        Extern Functions
********************************************************/
extern int GetWiFiType(void);
extern void wifi_driver_init(void);
extern void woal_interrupt(void);
#ifdef QUECTEL_PROJECT_CUST
extern void host_sdio_interrupt(void);
#endif
extern UINT8 check_if_DCS_mode(void);

UINT8 sdio_work(void)
{
    if(GetWiFiType() == 6)
    {
        if(!g_wifi_wake_no_lock || sdio_wake_lock)
            return 1;
        else
            return 0;
    }
    else
    {
        if (g_wifi_wake_no_lock)
            return 0;

        return sdio_wake_lock;
    }
}

inline static void sdio_set_wakelock()
{
    sdio_wake_lock = 1;
}

inline static void sdio_clear_wakelock()
{
    sdio_wake_lock = 0;
}

/*
Free memory used by sdio init task
*/
void DeleteSdioInitTimerFunc(UINT32 id)
{
    if(SDIOInitRef)
        OSATaskDelete(SDIOInitRef);

    if(SDIOInitStack)
        free(SDIOInitStack);

    OSATimerDelete(DeleteSdioInitTimer);
}

#if 0
/**
 *  @brief: output data in hex format
 *
 *  @prompt: prompt text
 *  @buf:  input buffer
 *  @len: data length
 *
 */
static inline void sdiohexdump(char *prompt, UINT8 * buf, int len)
{
	int 	i;
	char	dbgdumpbuf[64];
	char	*ptr = dbgdumpbuf;

	CPUartLogPrintf( "%s:\n", prompt);
	for (i = 1; i <= len; i++) {
		ptr += sprintf(ptr, "%02x ", *buf);
		buf++;
		if (i % 16 == 0) {
			*ptr = 0;
			CPUartLogPrintf( "%s\n", dbgdumpbuf);
			ptr = dbgdumpbuf;
		}
	}
	if (len % 16) {
		*ptr = 0;
		CPUartLogPrintf( "%s\n", dbgdumpbuf);
	}
}
#endif

static void sdio_wait(UINT32 count)
{
    UINT32 i = 0;
    for(i=0; i<count; i++)
    {;}
}

static void sdio_mswait(UINT32 count)
{
    UINT32 i = 0;
    UINT32 j = 0;
    for(i=0; i<count; i++)
        for(j=0; j<300000; j++)
        {;}
}

static void sdio_card_int_task(void *ptr)
{
    OSA_STATUS status;
    UINT32 flag_value;
    UINT32 flag_mask = SDIO_INTERRUPT_FLAG_BIT;

    while(1)
    {
        status = OSAFlagWait(SDIOFlgRef, flag_mask, OSA_FLAG_OR_CLEAR,&flag_value,OSA_SUSPEND);
		ASSERT(status == OS_SUCCESS);
		if(flag_value & SDIO_INTERRUPT_FLAG_BIT)
		{
			//RTI_LOG("sdio card interrupt!");
		#ifdef QUECTEL_PROJECT_CUST
	    	host_sdio_interrupt();
		#else
			woal_interrupt();
		#endif
		}
    }
}

static void sdio_create_card_int_task(void)
{
	OS_STATUS   status;
    status = OSAFlagCreate(&SDIOFlgRef);
    ASSERT(status == OS_SUCCESS);

	SDIOStack = malloc(SDIO_CARDINT_TASK_STACK_SIZE);
    status = OSATaskCreate(&SDIOTaskRef, SDIOStack, SDIO_CARDINT_TASK_STACK_SIZE, 65, "sdiocard", sdio_card_int_task, NULL);
    ASSERT(status == OS_SUCCESS);
}

static void sdio_create_semhisr(void)
{
    OS_STATUS   status;
    status = OSASemaphoreCreate (&SDIOSysRef, 1, OSA_FIFO);
    ASSERT(status == OS_SUCCESS);

	status = OSASemaphoreCreate (&sdio_sem_data, 0, OSA_FIFO);
    ASSERT(status == OS_SUCCESS);

    //create HISR
    OS_Create_HISR(&SdioHISR, "sdiohisr", sdio_hisr, HISR_PRIORITY_2);
}

static UINT8 sdio_readb(UINT32 base, UINT32 reg)
{
    return *(volatile UINT8*)(base + reg);
}

static void sdio_writeb(UINT32 base, UINT32 reg, UINT8 data)
{
    *(volatile UINT8*)(base + reg) = data;
}

static UINT32 sdio_readl(UINT32 base, UINT32 reg)
{
    return *(volatile UINT32*)(base + reg);
}

static void sdio_writel(UINT32 base, UINT32 reg, UINT32 data)
{
    *(volatile UINT32*)(base + reg) = data;
}

static UINT16 sdio_readw(UINT32 base, UINT32 reg)
{
    return *(volatile UINT16*)(base + reg);
}

static void sdio_writew(UINT32 base, UINT32 reg, UINT16 data)
{
    *(volatile UINT16*)(base + reg) = data;
}

static void sdio_irq_enable(UINT32 base_addr, UINT16 mask)
{
	volatile UINT16* ptr16;

	ptr16 = (UINT16*) (base_addr + SD_NORM_INTR_STS_EBLE_offset);
	*ptr16 |= mask ;

	ptr16 = (UINT16*) (base_addr + SD_NORM_INTR_STS_INTR_EBLE_offset);
	*ptr16 |= mask ;
}

static void sdio_errirq_enable(UINT32 base_addr, UINT16 mask)
{
	volatile UINT16* ptr16;

	ptr16 = (UINT16*) (base_addr + SD_ERR_INTR_STS_EBLE_offset);
	*ptr16 |= mask ;

	ptr16 = (UINT16*) (base_addr + SD_ERR_INTR_STS_INTR_EBLE_offset);
	*ptr16 |= mask ;
}



/**
 *  @brief: enable clock
 *  @base_addr: base address
 *  @clock: if clock == 0, no div; else div by 2*clock
 */
static void sdio_enable_clock(UINT32 base_addr, UINT32 CLOCK)
{
    volatile UINT16* ptr16;
    UINT32      tick_count;

    CLOCK = ((CLOCK&0x300)>>8)|((CLOCK&0xFF)<<2);
    ptr16 = (UINT16*) (base_addr + SD_CLOCK_CTRL_offset);
    *ptr16 = (((CLOCK & 0X3FF) << 6) | 0x01);

    /*Loop wait for sd clock and timeout are set*/
    tick_count = 100;
    while( !(*ptr16 & 0x0002 == 0x2) )
    {
        if(tick_count == 0)
        {
            CPUartLogPrintf("clock timeout \r\n");
			break;
        }
        sdio_mswait(1);
        tick_count--;
    }

    *ptr16 |= 0x0004;

    CPUartLogPrintf("SDIO:SD_CLOCK_CTRL_offset=%x", *ptr16);
}

static UINT32 sdio_sw_resetdata(UINT32 base_addr)
{
	volatile UINT16* ptr16;
	UINT32 timeout = 10000;
	UINT16 val;

#if 0
	CPUartLogPrintf("sdio_sw_resetdata\r\n");
#endif

	ptr16 = (UINT16*) (base_addr + SD_SW_RESET_CTRL_offset);
	val = *ptr16;

	val |= SW_RST_DAT;

	*ptr16 = val;

	do
    {
		val = *ptr16;
		if (!(val & (SW_RST_DAT)))
		{
			break;
		}

	} while (timeout--);

	if (timeout)
	{
		return MMC_ERR_NONE;
	}

	CPUartLogPrintf("sdio_sw_resetdata timeout\r\n");
	return MMC_ERR_CMD_TOUT;
}

#ifdef QUECTEL_PROJECT_CUST
static UINT32 sdio_abort()
#else
UINT32 sdio_abort()
#endif
{
    UINT8           data;
    UINT32          status = 0;

#ifndef QUECTEL_PROJECT_CUST
    if(GetWiFiType() == 6)
    {
        data = 0x08;
        SDIO_CMD52(1, 0, SDIO_CCCR_ABORT, data, 0);
    }
    else
#endif
    {
    //abort
    data = 0x01;
    status = SDIO_CMD52(1, 0, SDIO_CCCR_ABORT, data, 0);
    if(status != 0)
    {
        CPUartLogPrintf("SDIOERR:Abort Error");
        //return 1;
    }


    CPUartLogPrintf("sdio_abort 0");
    data = 0x08;
    status = SDIO_CMD52(1, 0, SDIO_CCCR_ABORT, data, 0);
    if(status != 0)
    {
        CPUartLogPrintf("sdio_abort Error");
        return 1;
    }

    CPUartLogPrintf("sdio_abort 1");
    sdio_mswait(1);
	}
    return 0;

}

static UINT32 sdio_sw_reset(UINT32 base_addr)
{
	volatile UINT16* ptr16;
	UINT32 timeout = 1000;
	UINT16 val;

	CPUartLogPrintf("sdio_sw_reset");

	ptr16 = (UINT16*) (base_addr + SD_SW_RESET_CTRL_offset);
	val = *ptr16;

	val |= SW_RST_CMD | SW_RST_DAT;

	*ptr16 = val;

	do
    {
		val = *ptr16;
		if (!(val & (SW_RST_DAT | SW_RST_CMD)))
			break;
		sdio_wait(1000);
	} while (timeout--);

	if (timeout)
		return MMC_ERR_NONE;

	CPUartLogPrintf("SDIOERR:Software reset timeout\r\n");
	return MMC_ERR_CMD_TOUT;
}

/**
 *  @brief: send cmd without data
 *
 *  @cmd: command code
 *  @argv:  argument
 *  @base_addr: base address
 *
 *  @return:    0 is success, otherwise is failure
 */
static UINT32 sdio_cmd_nodata(UINT16 cmd, UINT32 argv, UINT32 base_addr)
{
	volatile UINT32* ptr32;
	volatile UINT16* ptr16;
	volatile int tick_count;
    UINT32 mask;
	volatile int timeout = 0;


	/*Set wait*/
	CMD_COMP_status = 0;

	/*sdio irq init*/
	//sdio_card_int(1);
#if SDIO_DEBUG
	CPUartLogPrintf("%s enter: cmd=0x%x cmd_index=%d\n", __FUNCTION__, cmd, SDHCI_GET_CMD(cmd));
#endif
    timeout = 1000;
    mask = SDHCI_CMD_INHIBIT;
    while(timeout && (sdio_readl(base_addr, SD_PRESENT_STAT_0_offset))&mask)
    {
        timeout--;
    }
    if(timeout == 0)
    {
        CPUartLogPrintf("SDIOERR:Controller never released inhabit bit\r\n");
        sdio_sw_reset(SD_CTRL_BASE_ADDR);
        return MMC_ERR_CMD_TOUT;
    }


	/*put to execute*/
	ptr32 = (UINT32*) (base_addr + SD_ARG_LOW_offset);
	*ptr32 = argv;

    ptr16 = (UINT16*) (base_addr + SD_CMD_offset);
    *ptr16 = cmd;


	/*wait for complete*/
	tick_count = 100000;
	do
	{
	    if(CMD_COMP_status)
			break;
		tick_count--;
	}while (tick_count > 0);

    if (tick_count == 0 )
    {
        CPUartLogPrintf("sdio_cmd_nodata SDIOERR:Wait for command time out!\r\n");
        sdio_sw_reset(SD_CTRL_BASE_ADDR);
        return MMC_ERR_CMD_TOUT;
    }

	/*Get [0--7] Response registers*/
	ptr16 = (UINT16*) (base_addr + SD_RESP_0_offset);
	for (tick_count = 0; tick_count < 8; tick_count++)
	{
		MMC_RESPONSE[tick_count] = *ptr16;
		ptr16++;
	}
#if SDIO_DEBUG
	CPUartLogPrintf("%s leave\n", __FUNCTION__);
#endif
	return MMC_ERR_NONE;
}

/**
 *  @brief: This function send cmd53 with data
 *
 *  @cmd:   command code
 *  @arg:   argument
 *  @buf:   pointer to buffer used to be read or wrote
 *  @blocks:    block number
 *  @blksz:   if byte mode, block size
 *  @mode:  transfer mode
 *
 *  @return: 0 is success, otherwise is failure
 */
static int sdio_cmd_data(UINT16 cmd, UINT32 arg, UINT8* buf, unsigned blocks, unsigned blksz, UINT16 mode)
{
    volatile UINT32* ptr32;
    volatile UINT16* ptr16;
    volatile unsigned int timeout = 0;
    UINT32  mask;
    UINT8   ctrl;
    OS_STATUS status;
    int sem_num;
    UINT32 try_num = 20; // 1 senconds

    CMD_COMP_status = 0;
    DATA_COMP_status = 0;

    //set IRQ
    //sdio_card_int(1);

    timeout = 1000;
    mask = SDHCI_CMD_INHIBIT | SDHCI_DATA_INHIBIT;
    while(timeout && (sdio_readl(SD_CTRL_BASE_ADDR, SD_PRESENT_STAT_0_offset))&mask)
    {
        --timeout;
    }
    if(timeout == 0)
    {
        //sdhci_dumpregs(SD_CTRL_BASE_ADDR);
        CPUartLogPrintf("SDIOERR:53Controller %s never released inhabit bit %x\r\n", (mode&SDHCI_TRNS_READ)? "Read": "Write",
            (sdio_readl(SD_CTRL_BASE_ADDR, SD_PRESENT_STAT_0_offset)));
        sdio_sw_reset(SD_CTRL_BASE_ADDR);
        return MMC_ERR_CMD_TOUT;
    }

    //set timeout
    sdio_writeb(SD_CTRL_BASE_ADDR, SD_SW_RESET_CTRL_offset, 0x0E);

    //set host ctrl
	ctrl = sdio_readb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset);
	ctrl &= ~SDHCI_CTRL_DMA_MASK;
	ctrl |= SDHCI_CTRL_SDMA;
#if SDIO_DEBUG
    CPUartLogPrintf("cmd_data host ctrl = 0x%02x\r\n", ctrl);
#endif
	sdio_writeb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset, ctrl);

    //set block size and count
    sdio_writew(SD_CTRL_BASE_ADDR, SD_BLOCK_SIZE_offset, SDHCI_MAKE_BLKSZ(7, blksz));
	sdio_writew(SD_CTRL_BASE_ADDR, SD_BLOCK_COUNT_offset, blocks);

    //set dma addr
    sdio_writel(SD_CTRL_BASE_ADDR, SD_SYSADDR_LOW_offset, (UINT32)buf);

    //set arg
    ptr32 = (UINT32*) (SD_CTRL_BASE_ADDR + SD_ARG_LOW_offset);
	*ptr32 = arg;
#if SDIO_DEBUG
    CPUartLogPrintf("cmd_data arg = 0x%x\r\n", arg);

    //set transfer mode
    CPUartLogPrintf("cmd_data transfer mode = 0x%x\r\n", mode);
#endif
    sdio_writew(SD_CTRL_BASE_ADDR, SD_TRANSFER_MODE_offset, mode);

#if 0
    {
        int tmpdelay = 500;
        while(tmpdelay)
            tmpdelay--;
    }
#endif

	//set cmd
    ptr16 = (UINT16*) (SD_CTRL_BASE_ADDR + SD_CMD_offset);
    *ptr16 = cmd;

    timeout = 100000;
	while (--timeout)
	{
		if(CMD_COMP_status) break;
	}
    if (timeout == 0)
    {
        CPUartLogPrintf("SDIOERR:Wait for command time out!\r\n");
        sdio_sw_reset(SD_CTRL_BASE_ADDR);
        return MMC_ERR_CMD_TOUT;
    }

    ptr16 = (UINT16*)(SD_CTRL_BASE_ADDR + SD_RESP_0_offset);
	MMC_RESPONSE[0] = *ptr16;
#if SDIO_DEBUG
    CPUartLogPrintf("cmd53 resp0 = 0x%x\r\n", MMC_RESPONSE[0]);
#endif

	if (MMC_RESPONSE[0] & (R5_ERROR | R5_FUNCTION_NUMBER | R5_OUT_OF_RANGE))
	{
	    CPUartLogPrintf("SDIOERR:MMC_ERR_IO");
		return MMC_ERR_IO;
	}

TRY_LL:
    status = OSASemaphoreAcquire(sdio_sem_data, SDIO_DATA_TICK);
    if (status == OS_TIMEOUT)
    {
        if(intstsrec & XFER_COMP)
        {
            #if 0
            CPUartLogPrintf("sdio sema timeout,xfer completed");
            sem_num = -1;
            OSASemaphorePoll(sdio_sem_data, &sem_num);
            if(sem_num == 0)
            {
                OSASemaphoreRelease(sdio_sem_data);
            }
            #endif
        }
        else if(intstsrec & ADMA_ERR)
        {
            CPUartLogPrintf("sdio_cmd_data ADMA_ERR");
        }
        else if(--try_num)
        {
            #if 1
            CPUartLogPrintf("sdio_cmd_data: data timeout, blocks=%d blksz=%d try_num=%d", blocks, blksz, try_num);
            timeout = 100;
        	while (--timeout)
        	{
        		if(intstsrec & XFER_COMP)
        		{
                    return 0;
                }
        	}
            #endif
            CPUartLogPrintf("sdio_cmd_data: data timeout,try wait try_num=%d", try_num);
            goto TRY_LL;
        }
        else
        {
            //sdhci_dumpregs(SD_CTRL_BASE_ADDR);

            CPUartLogPrintf("sdio_cmd_data SDIOERR:INT status:%x, bufaddr:%x, blk:%d, R5:%x", intstsrec, bufaddr, blocks, MMC_RESPONSE[0]);

			CPUartLogPrintf("sdio_cmd_data SDIOERR:Wait for %s data transfer time out!\r\n",
			                    (mode&SDHCI_TRNS_READ)? "Read": "Write");
            sdio_sw_reset(SD_CTRL_BASE_ADDR);
			return MMC_ERR_DATA_TOUT;
        }
    }

    if(cmderror == 1)
    {
        return MMC_ERR_DATA_CRC;
    }

    return 0;

}

/**
 *  @brief: This function send cmd53 to sdio card
 *
 *  @write: 1 is write, 0 is read
 *  @fn:    number of funtion
 *  @addr:  register address
 *  @incr_addr: 1 is incrementing address, 0 is fixed address
 *  @buf:   pointer to buffer used to be read or wrote
 *  @blocks:    block number
 *  @blksz:   if byte mode, block size
 *
 *  @return: 0 is success, otherwise is failure
 */
int SDIO_CMD53(int write, unsigned fn, unsigned addr, int incr_addr,
    UINT8* buf, unsigned blocks, unsigned blksz)
{
    UINT16  cmd = 0;
    UINT32  arg = 0;
    int     ret = 0;
    UINT16 mode = 0;

    //ASSERT((UINT32)buf%4==0);

    cmd = 0x353a; //cmd53 3522
	arg = write ? 0x80000000 : 0x00000000;
	arg |= fn << 28;
	arg |= incr_addr ? 0x04000000 : 0x00000000;
	arg |= addr << 9;
	if (blocks == 1 && blksz <= 512)
		arg |= (blksz == 512) ? 0 : blksz;	/* byte mode */
	else
		arg |= 0x08000000 | blocks;		/* block mode */

    //compute transfer mode
    mode = SDHCI_TRNS_BLK_CNT_EN;
	if (blocks > 1)
		mode |= SDHCI_TRNS_MULTI;
	if (write == 0)
		mode |= SDHCI_TRNS_READ;
    mode |= SDHCI_TRNS_DMA; //use dma

    if(write == 0 && buf == NULL)
    {
        CPUartLogPrintf("SDIOERR:read buf is null\r\n");
        return MMC_ERR_INVALID;
    }
#if SDIO_DEBUG
    CPUartLogPrintf("send cmd53\r\n");
#endif

    ret = sdio_cmd_data(cmd, arg, buf, blocks, blksz, mode);
    if(ret) CPUartLogPrintf("SDIO:REDO, intstsrec=0x%x errintstsrec=0x%x", intstsrec, errintstsrec);

    return ret;
}

/**
 *  @brief: This function send cmd52 to sdio card
 *
 *  @write: 1 is write, 0 is read
 *  @fn:    number of funtion
 *  @addr:  register address
 *  @in:    if write, this is the write data
 *  @out:   if read, this return data
 *
 *  @return: 0 is success, otherwise is failure
 */
int SDIO_CMD52(int write, unsigned fn, unsigned addr, UINT8 in, UINT8* out)
{
    UINT32             cmd = 0;
    UINT32            argv = 0;
    UINT32              ret;
    OS_STATUS           status;

    status = OSASemaphoreAcquire(SDIOSysRef, OS_SUSPEND);
    ASSERT(status == OS_SUCCESS);

#if SDIO_DEBUG
    CPUartLogPrintf("send cmd52\r\n");
#endif
    cmd = 0x3402; //cmd52

    argv = write ? 0x80000000 : 0x00000000;
	argv |= fn << 28;
	argv |= (write && out) ? 0x08000000 : 0x00000000;
	argv |= addr << 9;
	argv |= in;
#if SDIO_DEBUG
    CPUartLogPrintf("cmd52 arg = 0x%08x\r\n", argv);
#endif
    ret = sdio_cmd_nodata(cmd, argv, SD_CTRL_BASE_ADDR);
    if(MMC_ERR_NONE != ret)
    {
        status = OSASemaphoreRelease(SDIOSysRef);
        ASSERT(status == OS_SUCCESS);
        return ret;
    }

    if(0 == write)
    {
        if(NULL == out)
        {
            CPUartLogPrintf("SDIOERR:out can't be NULL\r\n");
            status = OSASemaphoreRelease(SDIOSysRef);
	        ASSERT(status == OS_SUCCESS);
            return MMC_ERR_INVALID;
        }

        *out = MMC_RESPONSE[0] & 0xFF;
    }

    status = OSASemaphoreRelease(SDIOSysRef);
    ASSERT(status == OS_SUCCESS);
    return 0;
}

static void sdio_get_capa(UINT32 base)
{
    sdiocapa1 = sdio_readw(base, SDHCI_CAPABILITIES);
    sdiocapa2 = sdio_readw(base, SDHCI_CAPABILITIES2);
    sdiocapa3 = sdio_readw(base, SDHCI_CAPABILITIES3);
    sdiocapa4 = sdio_readw(base, SDHCI_CAPABILITIES4);
    CPUartLogPrintf("SDIO:SDHCI_CAPABILITIES1=0x%x", sdiocapa1);
    CPUartLogPrintf("SDIO:SDHCI_CAPABILITIES2=0x%x", sdiocapa2);
    CPUartLogPrintf("SDIO:SDHCI_CAPABILITIES3=0x%x", sdiocapa3);
    CPUartLogPrintf("SDIO:SDHCI_CAPABILITIES4=0x%x", sdiocapa4);
}

static int sdio_do_start_signal_voltage_switch(int signal_voltage)
{
    UINT8 pwr;
    UINT16 clk, ctrl;
    UINT32 present_state;

    CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
    ctrl = sdio_readw(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2);
    if(signal_voltage == SDHCI_SIGNAL_VOLTAGE_330)
    {
        //set 1.8v signal enable in the host control2 register to 0
        ctrl &= ~SDHCI_CTRL_VDD_180;
        sdio_writew(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2, ctrl);

        //wait for 5ms
        sdio_mswait(5);

        //3.3v regulator output should be stable within 5 ms
        ctrl = sdio_readw(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2);
        if(!(ctrl & SDHCI_CTRL_VDD_180))
            return 0;
        else
        {
            CPUartLogPrintf("switch to 3.3v signalling voltage failed");
            return -1;
        }
    }
    else if(!(ctrl & SDHCI_CTRL_VDD_180) && (signal_voltage == SDHCI_SIGNAL_VOLTAGE_180))
    {
        CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
        clk = sdio_readw(SD_CTRL_BASE_ADDR, SD_CLOCK_CTRL_offset);
        clk &= ~SDHCI_CLOCK_CARD_EN;
        sdio_writew(SD_CTRL_BASE_ADDR, SD_CLOCK_CTRL_offset, clk);

        //check whether data[3:0] is 0000
        CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
        present_state = sdio_readl(SD_CTRL_BASE_ADDR, SD_PRESENT_STAT_0_offset);
        if(!((present_state & SDHCI_DATA_LVL_MASK) >> SDHCI_DATA_LVL_SHIFT))
        {
            CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
            //enable 1.8v signal enable in the host control2 register
            ctrl |= SDHCI_CTRL_VDD_180;
            sdio_writew(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2, ctrl);

            sdio_mswait(5);

            CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
            ctrl = sdio_readw(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2);
            if(ctrl & SDHCI_CTRL_VDD_180)
            {
                CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
                clk = sdio_readw(SD_CTRL_BASE_ADDR, SD_CLOCK_CTRL_offset);
                clk = SDHCI_CLOCK_CARD_EN;
                sdio_writew(SD_CTRL_BASE_ADDR, SD_CLOCK_CTRL_offset, clk);
                sdio_mswait(1);

                CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
                //if data[3:0] level is 1111b, then the card was switched to 1.8 signaling
                present_state = sdio_readl(SD_CTRL_BASE_ADDR, SD_PRESENT_STAT_0_offset);
                if((present_state & SDHCI_DATA_LVL_MASK) == SDHCI_DATA_LVL_MASK)
                {
                    CPUartLogPrintf("sdio switch to 1.8 voltage success");
                    return 0;
                }
            }
        }

        CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
        pwr = sdio_readb(SD_CTRL_BASE_ADDR, SDHCI_POWER_CONTROL);
        pwr &= ~SDHCI_POWER_ON;
        sdio_writeb(SD_CTRL_BASE_ADDR, SDHCI_POWER_CONTROL, pwr);

        sdio_mswait(1);

        pwr |= SDHCI_POWER_ON;
        sdio_writeb(SD_CTRL_BASE_ADDR, SDHCI_POWER_CONTROL, pwr);
        CPUartLogPrintf("switching to 1.8v signalling voltage failed");

        return -1;
    }
    else
        return 0;
}

static int sdio_set_signal_voltage(int signal_voltage)
{
    int err = 0;
    if(signal_voltage != SDHCI_SIGNAL_VOLTAGE_330)
    {
        CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
        //send cmd11
        UINT32 ret = sdio_cmd_nodata(0x0B02, 0, SD_CTRL_BASE_ADDR);
        if(ret)
            err = -1;
        else
        {
            //voltage switch
            CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
            if(sdio_readw(SD_CTRL_BASE_ADDR, SDHCI_HOST_VERSION)<SDHCI_SPEC_300)
            {
                CPUartLogPrintf("sdio ver is lower than 3.0");
            }
            else
            {
                CPUartLogPrintf("%s %d", __FUNCTION__, __LINE__);
                err = sdio_do_start_signal_voltage_switch(signal_voltage);
            }
        }
    }
    return err;
}

/**
 *  @brief: Split an arbitrarily sized data transfer into several used cmd53.
 *
 *  @fn: number of function
 *  @write: 1 is write, 0 is read
 *  @addr:  register address
 *  @incr_addr: 1 is incrementing address, 0 is fixed address
 *  @buf: pointer to buffer used to be read or wrote
 *
 *  @return: 0 is success, otherwise is failure
 */
int sdio_io_rw_ext_helper(unsigned fn, int write,
	unsigned addr, int incr_addr, UINT8 *buf, unsigned size)
{
	unsigned remainder = size;
	unsigned max_blocks;
	int ret;
    unsigned fnblsz = sdiofunc[fn].cur_blksize;
    OSA_STATUS status;
#if SDIO_DEBUG
    CPUartLogPrintf("size is %d, fn-blocksize is %d\r\n", size, fnblsz);
#endif

    bufaddr = buf;

    status = OSASemaphoreAcquire(SDIOSysRef, OS_SUSPEND);
    ASSERT(status == OS_SUCCESS);

	/* Do the bulk of the transfer using block mode (if supported). */
	if (sdiocccr.multi_block && (size > fnblsz)) {
		/* Blocks per command is limited by host count, host transfer
		 * size (we only use a single sg entry) and the maximum for
		 * IO_RW_EXTENDED of 511 blocks. */
        max_blocks = 511;

		while (remainder > fnblsz) {
			unsigned blocks;

			blocks = remainder / fnblsz;
			if (blocks > max_blocks)
				blocks = max_blocks;
			size = blocks * fnblsz;

			ret = SDIO_CMD53(write, fn, addr, incr_addr, buf, blocks, fnblsz);
			if (ret)
			{
			    status = OSASemaphoreRelease(SDIOSysRef);
		        ASSERT(status == OS_SUCCESS);
				return ret;
			}

			remainder -= size;
			buf += size;
			if (incr_addr)
			{
				addr += size;
			}

		}
	}

	/* Write the remainder using byte mode. */
	while (remainder > 0) {
		size = min(remainder, fnblsz);

		ret = SDIO_CMD53(write, fn, addr, incr_addr, buf, 1, size);
		if (ret)
		{
			status = OSASemaphoreRelease(SDIOSysRef);
			ASSERT(status == OS_SUCCESS);
			return ret;
		}

		remainder -= size;
		buf += size;
		if (incr_addr)
			addr += size;
	}
    status = OSASemaphoreRelease(SDIOSysRef);
    ASSERT(status == OS_SUCCESS);
	return 0;
}

int sdio_clk_on(void)
{
	volatile UINT32* ptr32 = (UINT32*) (SD0_HOST_PMU_AXI_CLOCK);

    *ptr32 |= ((1<<3)|(1<<0));
   	ptr32 = (UINT32*) (SD1_HOST_PMU_AXI_CLOCK) ;
    if(is_sdioclk_48M)
        *ptr32 = 0x892; //SDH1_CLK_SEL 2b'10, 48MHz. SDH1_CLK_DIV 3b'000, div=1.
    else
    {
        *ptr32 &= ~(7<<5);	//0=416MHz,1=350MHz,2=312MHz
        *ptr32 |= ((1<<4)|(1<<1)|(1<<11)|(1<<8));
   	}
	sdio_mswait(10);

	CPUartLogPrintf("SDIO:SD0_HOST_PMU_AXI_CLOCK=0x%x,SD1_HOST_PMU_AXI_CLOCK=0x%x", *(UINT32*) (SD0_HOST_PMU_AXI_CLOCK),
						*(UINT32*) (SD1_HOST_PMU_AXI_CLOCK));
	return 0;
}


/**
 *  @brief This function set voltage.
 *
 *  @return MLAN_STATUS_SUCCESS or MLAN_STATUS_FAILURE
 */
static int sdio_set_power()
{
	volatile UINT16*  ptr16;


	ptr16 = (UINT16*) (SD_CTRL_BASE_ADDR + SD_HOST_CTRL_offset);

    if(0x0F == ((*ptr16)>>8))
    {
        return 0;
    }
    else
    {
        //set 1.8v
		*ptr16 |= 0x0A00;
        //enable
		*ptr16 |= 0x0100;

        CPUartLogPrintf("SDIO:SD_HOST_CTRL_offset=%x", *((UINT16*) (SD_CTRL_BASE_ADDR + SD_HOST_CTRL_offset)));
    }

    return 0;

}


//static
int sdio_read_cccr(sdio_cccr *cccr)
{
    UINT8           data = 0;
    UINT32          status = 0;
    int             cccr_vsn = 0;
    int             uhs = bkocr & R4_18V_PRESENT;
    UINT8           speed = 0;

    if(GetWiFiType() == 6)
        uhs = 1; //HoopoeL forcely configure UHS
    status = SDIO_CMD52(0, 0, SDIO_CCCR_CCCR, 0, &data);
    if(MMC_ERR_NONE != status)
    {
        CPUartLogPrintf("SDIOERR:Read CCCR");
        return MMC_ERR_CMD_TOUT;
    }
    else
    {
        cccr->sdio_vsn = (data & 0xf0) >> 4;
        cccr_vsn = data & 0x0f;
        CPUartLogPrintf("SDIO:CCCR: 0x%02x cccr_vsn=%d uhs=%d", data, cccr_vsn, uhs);
    }

    status = SDIO_CMD52(0, 0, SDIO_CCCR_SD, 0, &data);
    if(MMC_ERR_NONE != status)
    {
        CPUartLogPrintf("SDIOERR:Read CCCR SD");
        return MMC_ERR_CMD_TOUT;
    }
    else
    {
        CPUartLogPrintf("SDIO:SD SPEC: 0x%02x", data);
    }

    //card capacity
    status = SDIO_CMD52(0, 0, SDIO_CCCR_CAPS, 0, &data);
    if(MMC_ERR_NONE != status)
    {
        CPUartLogPrintf("SDIOERR:Read CCCR CAPS");
        return MMC_ERR_CMD_TOUT;
    }
    else
    {
        #if 1
        CPUartLogPrintf("SDIO:card caps: 0x%02x", data);
        #endif
        if (data & SDIO_CCCR_CAP_SMB)
        {
            cccr->multi_block = 1;
            CPUartLogPrintf("SDIO:Support multi-block xfers(CMD53)");
        }
	    if (data & SDIO_CCCR_CAP_LSC)
        {
            cccr->low_speed = 1;
        
   CPUartLogPrintf("SDIO:low speed card");
        }
	    if (data & SDIO_CCCR_CAP_4BLS)
		{
		    cccr->wide_bus = 1;
		    CPUartLogPrintf("SDIO:4 bit low speed card");
	    }
    }

    //support power
    status = SDIO_CMD52(0, 0, SDIO_CCCR_POWER, 0, &data);
    if(MMC_ERR_NONE != status)
    {
        CPUartLogPrintf("SDIOERR:Read CCCR POWER");
        return MMC_ERR_CMD_TOUT;
    }
    else
    {
        #if 1
        CPUartLogPrintf("SDIO:card power: 0x%02x", data);
        #endif
        if (data & SDIO_POWER_SMPC)
        {
            cccr->high_power = 1;
            CPUartLogPrintf("SDIO:Supports Master Power Control");
        }
    }

    //support speed
    status = SDIO_CMD52(0, 0, SDIO_CCCR_SPEED, 0, &speed);
    if(MMC_ERR_NONE != status)
    {
        CPUartLogPrintf("SDIOERR:Read CCCR SPEED");
        return MMC_ERR_CMD_TOUT;
    }
    else
    {
        if(cccr_vsn >= SDIO_CCCR_REV_3_00 && uhs)
        {
            status = SDIO_CMD52(0, 0, SDIO_CCCR_UHS, 0, &data);
            if(status)
            {
                CPUartLogPrintf("SDIOERR:Read SDIO_CCCR_UHS");
                return MMC_ERR_CMD_TOUT;
            }

            CPUartLogPrintf("SDIO_CCCR_UHS:%x", data);

            if(sdiocapa3 & (SDR104_SUPPORT|SDR50_SUPPORT|DDR50_SUPPORT))
            {
                if(data & DDR50_SUPPORT)
                    sd3_bus_mode |= SD_MODE_UHS_DDR50;

                if(data & SDR50_SUPPORT)
                    sd3_bus_mode |= SD_MODE_UHS_SDR50;

                if(data & SDR104_SUPPORT)
                    sd3_bus_mode |= SD_MODE_UHS_SDR104;
            }

            CPUartLogPrintf("sdiocapa3:0x%x", sd3_bus_mode);

            status = SDIO_CMD52(0, 0, SDIO_CCCR_DRIVE_STRENGTH, 0, &data);
            if(status)
            {
                CPUartLogPrintf("SDIOERR:Read SDIO_CCCR_DRIVE_STRENGTH");
                return MMC_ERR_CMD_TOUT;
            }

            if(data & SDIO_DRIVE_SDTA)
                sd3_drv_type |= SD_DRIVER_TYPE_A;

            if(data & SDIO_DRIVE_SDTC)
                sd3_drv_type |= SD_DRIVER_TYPE_C;

            if(data & SDIO_DRIVE_SDTD)
                sd3_drv_type |= SD_DRIVER_TYPE_D;

        }

        if (sd3_bus_mode == 0)
        {
            if (speed & SDIO_SPEED_SHS)
            {
                cccr->high_speed = 1;
                CPUartLogPrintf("SDIO:Supports High-Speed mode");
            }
        }
    }

    return 0;
}

/**
 *  @brief This function initialize sdio card.
 *
 *  @return MLAN_STATUS_SUCCESS or MLAN_STATUS_FAILURE
 */

int sdio_cmd_init()
{
    UINT32          status = 0;
    UINT16             cmd = 0;
	UINT32            argv = 0;
	UINT32             ocr = 0;
    int              count = 0;
    UINT32       base_addr = SD_CTRL_BASE_ADDR;
    /*
    cmd0 set card to idle, cmd8 check the version,
    cmd0 -> cmd8 -> r7 ->cmd5 ->r4 ->cmd3 ->r6 ->cmd7 ->r1b
    */
#ifdef QUECTEL_PROJECT_CUST
#ifdef AICWIFI_SUPPORT
	if((GetWiFiType() == 3)||(GetWiFiType() == 4))
#else
    if(GetWiFiType() == 3)
#endif
    {
	    /*Invoke CMD0 */
        #if SDIO_DEBUG
        CPUartLogPrintf("Send CMD0");
        #endif
    	status = sdio_cmd_nodata(0x0000, 0x00000000, base_addr);
    	if (status != MMC_ERR_NONE)
    	{
    		CPUartLogPrintf("SDIOERR:Error Invoke CMD0");
    	}
        else
        {
            #if SDIO_DEBUG
            CPUartLogPrintf("CMD0 Done!");
            #endif
        }

        /*Invoke CMD8 */
        #if SDIO_DEBUG
        CPUartLogPrintf("Send CMD8");
        #endif
    	cmd = 0x0802;
        /*set ocr value to the voltage, 0xAA is check pattern*/
    	//argv = ((MMC_OCR_AVAIL & 0xFF8000) != 0) << 8 | 0xAA;
    	argv = (((MMC_OCR_AVAIL | MMC_VDD_165_195) & 0xFF8080) != 0) << 8 | 0xAA;
    	status = sdio_cmd_nodata(cmd , argv, base_addr);
    	if (status != MMC_ERR_NONE)
    	{
    		CPUartLogPrintf("SDIOERR:Error Invoke CMD8");
    	}
        else
    	{
    	    UINT8 result = MMC_RESPONSE[0]&0xFF;
            if(result != 0xAA)
            {
                CPUartLogPrintf("SDIOERR:CMD8 error");
            }

    	    #if SDIO_DEBUG
            /*cmd8's response is r7, r7 equal resp0 in this program,
            resp0[15:8]=voltage, resp0[7:0]=check pattern*/
            CPUartLogPrintf("Response CMD8: %x", MMC_RESPONSE[0]);
    		//CPUartLogPrintf("result: %x \r\n", MMC_RESPONSE[1]);
            CPUartLogPrintf("CMD8 Done!");
            #endif
    	}
    }
#endif
    /*Invoke CMD5*/
    #if SDIO_DEBUG
    CPUartLogPrintf("Send CMD5");
    #endif
	cmd = 0x0502;  /*test for SDIO device, not likely*/
	status = sdio_cmd_nodata(cmd , 0x00, base_addr);
	if (status != MMC_ERR_NONE)
	{
		CPUartLogPrintf("SDIOERR:Error Invoke CMD5");
        //return MMC_ERR_CMD_TOUT;
	}
    else
    {
        /*cmd5's response is r4, r4[31:8] is ocr(24-bits), in this program,
        resp0 is [23:8], resp1 is [39:24]
        */
    	ocr = (MMC_RESPONSE[1]<< 16) | MMC_RESPONSE[0];
        #if SDIO_DEBUG
        CPUartLogPrintf("CMD5 | ocr before: %x", ocr);
        #endif
        bkocr = ocr;    //save ocr for use later
        //ocr contain function number
        funcnumber = (ocr & 0x70000000) >> 28;
        ocr &= 0x00FFFFFF; //
        #if SDIO_DEBUG
        CPUartLogPrintf("CMD5 | ocr after: %x", ocr);
		CPUartLogPrintf("CMD5 Done!");
        #endif
    }

	count = 10;
	while (count>0)
    {
    	/*Invoke CMD5*/
    	cmd = 0x0502;  /*test for SDIO device, not likely*/
    	status = sdio_cmd_nodata(cmd , ocr, base_addr);
    	if (status != MMC_ERR_NONE)
    	{
    		CPUartLogPrintf("SDIOERR:Error Invoke CMD5");
    	}
        else
        {
            #if SDIO_DEBUG
    		CPUartLogPrintf("CMD5 Done!");
            #endif
    	}

        /*check response to get whether card power up status bit is set*/
        /*cmd5's response is r4*/
        /*resp1 is [39:24], bit[39] is 1 if Card is ready to operate
        after initialization*/
    	if ( MMC_RESPONSE[1] & 0x8000 )
    	{
    	    break;
    	}

        /*not ready, continue*/
        sdio_mswait(1);
    	count--;

        if(count == 0)
        {
            return MMC_ERR_CMD_TOUT;
        }
	}

    if((bkocr & R4_18V_PRESENT) && (sdiocapa3 & (SDR104_SUPPORT|SDR50_SUPPORT|DDR50_SUPPORT)))
    {
        CPUartLogPrintf("SDIO support 3.0");
        //needn't switch, the voltage is 1.8v default
        //sdio_set_signal_voltage(SDHCI_SIGNAL_VOLTAGE_180);
    }
    else
    {
        bkocr &= ~R4_18V_PRESENT;
    }

	/*Invoke CMD3, cmd3 get the RCA*/
    #if SDIO_DEBUG
    CPUartLogPrintf("Send CMD3");
    #endif
	status = sdio_cmd_nodata(0x0302, 0x00000000, base_addr);
	if (status != MMC_ERR_NONE)
	{
		CPUartLogPrintf("SDIOERR:Error Invoke CMD3");
        return MMC_ERR_CMD_TOUT;
	}
    else
	{
        /*read RCA, cmd3's response is R6, R6[39:24] is RCA, R6[23:8] is card status
        in this program, resp1 is RCA*/
        #if SDIO_DEBUG
    	CPUartLogPrintf("CMD3 Response: %x, %x", MMC_RESPONSE[0], MMC_RESPONSE[1]);
        #endif
    	MMC_RCA = MMC_RESPONSE[1] & 0xffff;
        #if SDIO_DEBUG
    	CPUartLogPrintf("Card RCA: %x", MMC_RCA);
        CPUartLogPrintf("CMD3 Done!");
        #endif
	}


	/*Invoke CMD7, cmd7 is select card, its argument[31:16] is RCA,other bits are stuff*/
	argv = MMC_RCA << 16;
	//argv = 0x10000;
	status = sdio_cmd_nodata(0x0702, argv, base_addr);
	if (status != MMC_ERR_NONE)
	{
		CPUartLogPrintf("SDIOERR:Error Invoke CMD7");
        return MMC_ERR_CMD_TOUT;
	}
    else
    {
        #if SDIO_DEBUG
        /*cmd7's response is R1b, R1b is identical to R1 with an optional busy
        signal transmitted on the data line, R1[39:8] is card status, in this program,
        resp0 and resp1 is represent these bits*/
        CPUartLogPrintf("CMD7 Response: %x, %x", MMC_RESPONSE[0], MMC_RESPONSE[1]);
		CPUartLogPrintf("CMD7 Done!");
        #endif
	}

    return 0;
}

void sdio_config_nzc_mifi_dkb_pin(int wifi_pwr_en, int wifi_pwr_reset, int host_wake_wifi, int wifi_wake_host)
{
    volatile unsigned long *r;
    unsigned int mfpr_val = 0x18C0;
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: input pin: wifi_pwr_en=%d wifi_pwr_reset=%d host_wake_wifi=%d wifi_wake_host=%d",
        wifi_pwr_en, wifi_pwr_reset, host_wake_wifi, wifi_wake_host);

#ifndef SSVWIFI_FUNCTION
    WIFI_PWREN_PIN = wifi_pwr_en;
    WIFI_INT_PIN = wifi_wake_host;
    WIFI_RESET_PIN = wifi_pwr_reset;
    HOST_WAKEUP_WIFI = host_wake_wifi;
    mfpr_val = 0x1840;
#endif

    //config sdio
    r = (volatile unsigned long*)(0xd401E300);  //clk
#ifdef QUECTEL_PROJECT_CUST
    *r = 0x18C0;
#else
	*r = 0xb040;
#endif
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_CLK MFPR(0x%x)=0x%x", r, *r);
    //RTI_LOG("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_CLK MFPR(0x%x)=0x%x", r, *r);

    r = (volatile unsigned long*)(0xd401E2FC);  //cmd
    *r = mfpr_val;
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_CMD MFPR(0x%x)=0x%x", r, *r);
    //RTI_LOG("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_CMD MFPR(0x%x)=0x%x", r, *r);

    r = (volatile unsigned long*)(0xd401E2F8);  //data0
    *r = mfpr_val;
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_DATA0 MFPR(0x%x)=0x%x", r, *r);
    //RTI_LOG("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_DATA0 MFPR(0x%x)=0x%x", r, *r);

    r = (volatile unsigned long*)(0xd401E2F4);  //data1
    *r = mfpr_val;
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_DATA1 MFPR(0x%x)=0x%x", r, *r);
    //RTI_LOG("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_DATA1 MFPR(0x%x)=0x%x", r, *r);

    r = (volatile unsigned long*)(0xd401E2F0);  //data2
    *r = mfpr_val;
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_DATA2 MFPR(0x%x)=0x%x", r, *r);
    //RTI_LOG("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_DATA2 MFPR(0x%x)=0x%x", r, *r);

    r = (volatile unsigned long*)(0xd401E2EC);  //data3
    *r = mfpr_val;
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_DATA3 MFPR(0x%x)=0x%x", r, *r);
    //RTI_LOG("sdio_config_nzc_mifi_dkb_pin A0: read MMC1_DATA3 MFPR(0x%x)=0x%x", r, *r);

    if(PlatformGetPMICType() == PMIC_GUILIN_LITE)
    {
        r = (volatile unsigned long*)(0xd401E0C8);  //1903SC GPIO122 fn2 -> 32K clock
        *r = 0x18C2;
    }
    else
    {
        r = (volatile unsigned long*)(0xd401E188);  //1903SR GPIO43 fn2 -> 32K clock
        *r = 0x18C2;
    }

	{
        volatile unsigned int temp = 0;
        unsigned int addr = 0xD401E0D4; // VCXO_REQ, wlan 26M clock req
        temp = (*(volatile unsigned long *)(addr));
        temp = temp & ~0x07;
        temp |= 0x0; // function 0
        (*(volatile unsigned long *)(addr)) = temp;
    }

	{
        volatile unsigned int temp = 0;
        unsigned int addr = 0xD4050000 + 0x18; // VCTCXO SW request control register

        temp = (*(volatile unsigned long *)(addr));
        temp |= 0x01; // bit 0: enable VCXO_REQ for VCXO_OUT
        (*(volatile unsigned long *)(addr)) = temp;
    }

#if defined(AICWIFI_SUPPORT) && defined (QUECTEL_PROJECT_CUST)
    r = (volatile unsigned long*)GPIO_MFPR_ADDR(wifi_pwr_reset);
    *r = 0x1040;

    //rst
    GPIOReturnCode err_code = GPIORC_OK;
    err_code = GpioSetDirection(wifi_pwr_reset, GPIO_OUT_PIN);
	CPUartLogPrintf("wifi_pwr_reset1: %d, %d\n", wifi_pwr_reset, err_code);
#if 0 //初始化直接拉高，无需先拉低
	
    err_code = GpioSetLevel(wifi_pwr_reset, GPIORC_LOW);
	CPUartLogPrintf("wifi_pwr_reset2: %d, %d\n", wifi_pwr_reset, err_code);

	sdio_mswait(1000);

    CPUartLogPrintf("wifi_pwr_reset: %d, %d\n", wifi_pwr_reset, GpioGetLevel((UINT32)wifi_pwr_reset));
#endif
    //high level
    err_code = GpioSetLevel(wifi_pwr_reset, GPIORC_HIGH);
	CPUartLogPrintf("wifi_pwr_reset2: %d, %d\n", wifi_pwr_reset, err_code);

	sdio_mswait(1000);

    CPUartLogPrintf("wifi_pwr_reset: %d, %d\n", wifi_pwr_reset, GpioGetLevel((UINT32)wifi_pwr_reset));

    #if 0
    r = (volatile unsigned long*)GPIO_MFPR_ADDR(wifi_pwr_en);
    *r = 0x1040;

    //pdn //heron ldo_en
    GpioSetDirection(wifi_pwr_en, GPIO_OUT_PIN);

    //heron ldo_en to high level
    GpioSetLevel(wifi_pwr_en, GPIORC_HIGH);
    #endif
#else
#ifndef SSVWIFI_FUNCTION
	r = (volatile unsigned long*)GPIO_MFPR_ADDR(wifi_pwr_en); //0xD401E288 wib_pdn gpio 83
    *r = 0xd040;
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: read wifi_pwr_en MFPR(0x%x)=0x%x", r, *r);

	r = (volatile unsigned long*)GPIO_MFPR_ADDR(wifi_pwr_reset); //0xD401E1F4 rst gpio 55
    *r = 0xd040;
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: read wifi_pwr_reset MFPR(0x%x)=0x%x", r, *r);
    //WIFI_INT_PIN
    r = (volatile unsigned long*)GPIO_MFPR_ADDR(wifi_wake_host);
    *r = 0xB020;
#ifndef QUECTEL_PROJECT_CUST
	GpioBindWakeupCallback(wifi_wake_host, NULL);
#endif
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: read wifi_wake_host MFPR(0x%x)=0x%x", r, *r);
    //HOST_WAKE_WLAN
    r = (volatile unsigned long*)GPIO_MFPR_ADDR(host_wake_wifi);
    *r = 0xB040; //pull dn enable
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: read host_wake_wifi MFPR(0x%x)=0x%x", r, *r);

	//rst
	GpioSetDirection(wifi_pwr_reset, GPIO_OUT_PIN);

	GpioSetLevel(wifi_pwr_reset, GPIORC_LOW);
    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: pull HW_RST down");
    sdio_mswait(1000);

	//high level
	GpioSetLevel(wifi_pwr_reset, GPIORC_HIGH);

    CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: pull HW_RST up");

#else
	r = (volatile unsigned long*)GPIO_MFPR_ADDR(wifi_pwr_en);
	*r = 0x1040;
	GpioSetDirection(wifi_pwr_en, GPIO_OUT_PIN);
	GpioSetLevel(wifi_pwr_en, GPIORC_HIGH);

	r = (volatile unsigned long*)GPIO_MFPR_ADDR(host_wake_wifi);
	*r = 0x1040;
	GpioSetDirection(host_wake_wifi, GPIO_OUT_PIN);
	GpioSetLevel(host_wake_wifi, GPIORC_HIGH);

	//wakeup host, edge detect
	volatile unsigned int data = *((volatile unsigned int*)(GPIO_MFPR_ADDR(wifi_wake_host)));
	data = data & ~(0x7);						//function 0
	data = data & ~(0x40)| 0x10;				// bit 6=0,edge_clear enable; bit 4=1,rise enable
	*((volatile unsigned long*)(GPIO_MFPR_ADDR(wifi_wake_host))) = data;
	GpioSetDirection(wifi_wake_host, GPIO_IN_PIN);

#endif
#endif

    if(GetWiFiType() == 3 || GetWiFiType() == 6)
    {
    	r = (volatile unsigned long*)(0xD4090000 + 0x110);
    	*r &= 0x8fffffff;
        CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: read APB_spare5_reg 0x%x is 0x%x", r, *r);

    	r = (volatile unsigned long*)(0xD4050000 + 0x18);
    	*r |= (1<<0);
        CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: read PMU_VRCR_reg 0x%x is 0x%x", r, *r);

    	r = (volatile unsigned long*)0xD401E0D4;
        CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: read vcxo_req_mfpr 0x%x is 0x%x", r, *r);
    	r = (volatile unsigned long*)0xD401E0CC;
        CPUartLogPrintf("sdio_config_nzc_mifi_dkb_pin: read clk_req_mfpr 0x%x is 0x%x", r, *r);
    }

    return;
}

void sdio_burstsize_config(void)
{
    volatile UINT16* ptr16;
    UINT32       base_addr = SD_CTRL_BASE_ADDR;

    //SD_CLOCK_AND_BURST_SIZE_SETUP_offset is 0xE6 in A0&Z0 chip
    /*set sd_clk delay */
	ptr16 = (UINT16*) (base_addr + 0xE6);

    //*ptr16 |= 0x03C1;
    *ptr16 = 0xC5;

    CPUartLogPrintf("SDIO:SD_CLOCK_AND_BURST_SIZE_SETUP %x", sdio_readw(base_addr,0xE6));
}

int sdio_enable_hs(const sdio_cccr *cccr)
{
    UINT32 status = 0;
    UINT8   speed = 0;
    UINT8   data = 0;

    if(0 == cccr->high_speed)
    {
        CPUartLogPrintf("SDIO:Not support high speed");
        return 0;
    }

    status = SDIO_CMD52(0, 0, SDIO_CCCR_SPEED, 0, &speed);
    if(MMC_ERR_NONE != status)
    {
        CPUartLogPrintf("SDIOERR:Read cccr speed error");
        return MMC_ERR_CMD_TOUT;
    }

    speed |= SDIO_SPEED_EHS;
    CPUartLogPrintf("speed = 0x%02x\r\n", speed);

    status = SDIO_CMD52(1, 0, SDIO_CCCR_SPEED, speed, NULL);
    if(MMC_ERR_NONE != status)
    {
        CPUartLogPrintf("SDIOERR:Write cccr speed error");
        return MMC_ERR_CMD_TOUT;
    }

#if SDIO_DEBUG
    status = SDIO_CMD52(0, 0, SDIO_CCCR_SPEED, 0, &speed);
    if(status == 0){
        CPUartLogPrintf("speed = 0x%02x", speed);
    }
#endif

    data = sdio_readb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset);
    //enable host ctrl

    data |= SDHCI_CTRL_HISPD;

    sdio_writeb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset, data);

    return 0;

}

//static //CQ00132191 modify
int sdio_endabe_widebus(const sdio_cccr *cccr)
{
    UINT32 status = 0;
    UINT8    ctrl = 0;
    UINT8    data = 0;

	if (cccr->low_speed && !cccr->wide_bus)
	{
	    CPUartLogPrintf("SDIO:not support wide bus");
        return 0;
	}

    status = SDIO_CMD52(0, 0, SDIO_CCCR_IF, 0, &ctrl);
    if(MMC_ERR_NONE != status)
    {
        CPUartLogPrintf("SDIOERR:Read bus if error");
        return MMC_ERR_CMD_TOUT;
    }

    ctrl |= SDIO_BUS_WIDTH_4BIT;

    status = SDIO_CMD52(1, 0, SDIO_CCCR_IF, ctrl, NULL);
    if(MMC_ERR_NONE != status)
    {
        CPUartLogPrintf("SDIOERR:Write bus if error");
        return MMC_ERR_CMD_TOUT;
    }


    data = sdio_readb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset);
    //enable 4-bit bus
    data |= SDHCI_CTRL_4BITBUS;
    sdio_writeb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset, data);

#if SDIO_DEBUG
    CPUartLogPrintf("SDIO:HOST CTRL REG = 0x%02x", sdio_readb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset));
#endif

    return 0;
}

/**
 *  @brief This function get function interface code from FBR.
 *
 *  @num: number of function
 *  @data: pointer to interface code
 *  @return 	   MLAN_STATUS_SUCCESS or MLAN_STATUS_FAILURE
 */
int sdio_read_fbr(int num, UINT8 *data)
{
	int    ret = 0;

    ret = SDIO_CMD52(0, 0, SDIO_FBR_BASE(num) + SDIO_FBR_STD_IF, 0, data);
	if (ret)
		goto out;

	*data &= 0x0f;

	if (*data == 0x0f) {
        ret = SDIO_CMD52(0, 0, SDIO_FBR_BASE(num) + SDIO_FBR_STD_IF_EXT, 0, data);
		if (ret)
			goto out;
	}

out:
	return ret;
}

/**
 *  @brief This function enable related device.
 *
 *  @num: number of function
 *  @return 	   MLAN_STATUS_SUCCESS or MLAN_STATUS_FAILURE
 */
int sdio_enable_function(UINT8 num)
{
    UINT8   data = 0;
    int      ret = 0;
    int  timeout = 0;
#ifdef QUECTEL_PROJECT_CUST
#ifdef AICWIFI_SUPPORT
    if (num == 0) {
        return 0;
    }
    ret = -1;
#endif
#endif

    ret = SDIO_CMD52(0, 0, SDIO_CCCR_IOEx, 0, &data);
    if(ret)
        goto err;

    data |= 1<<num;
    ret = SDIO_CMD52(1, 0, SDIO_CCCR_IOEx, data, NULL);
    if(ret)
        goto err;

    CPUartLogPrintf("SDIO:CCCR IOEX = 0x%x", data);

    timeout = 100;
    while (1) {
		ret = SDIO_CMD52(0, 0, SDIO_CCCR_IORx, 0, &data);
        if(ret)
            goto err;
		if (data & (1 << num))
			break;

        timeout--;
        if(0 == timeout)
            goto err;

        sdio_mswait(1);
	}
#ifdef QUECTEL_PROJECT_CUST
#ifdef AICWIFI_SUPPORT
    sdiofnnum = num;
#endif
#endif
    return 0;
err:
    CPUartLogPrintf("SDIOERR:Enable SDIO FUNC");
    return ret;
}

/**
 *  @brief This function enable functions and get interface code from their FBR.
 *
 *  @param
 *  @return
 */
void sdio_read_functions()
{
    int      i = 0;
    UINT8 data = 0;
    int    ret = 0;

    for(i=0; i<funcnumber; i++)
    {
        ret = sdio_enable_function(i+1);
        CPUartLogPrintf("ret = %d\r\n", ret);
        if(ret)
        {
            CPUartLogPrintf("SDIOERR:Read func error, i=%d\r\n", i);
            return;
        }
    }

    #if 1
    ret = SDIO_CMD52(0, 0, SDIO_CCCR_IOEx, 0, &data);
    if(ret)
        return;
    CPUartLogPrintf("SDIO:CCCR IOEX = %x\r\n", data);
    #endif

    for(i=0; i<funcnumber; i++)
    {
        data = 0;
        ret = sdio_read_fbr(i+1, &data);
        if(!ret)
        {
            CPUartLogPrintf("SDIO:Func%d = 0x%02x\r\n", i+1, data);
        }
    }
}

/**
 *  @brief This function set block size of function.
 *
 *  @fn: number of function
 *  @blksz: block size
 *  @return MLAN_STATUS_SUCCESS or MLAN_STATUS_FAILURE
 */
int sdio_set_block_size(unsigned fn, unsigned blksz)
{
	int ret;

#ifdef QUECTEL_PROJECT_CUST
#ifdef AICWIFI_SUPPORT
    {
        sdiofunc[fn].cur_blksize = blksz;
        return 0;
    }
#else
	if (blksz > 512u)
		return MMC_ERR_INVALIDARG;

	ret = SDIO_CMD52(1, 0, SDIO_FBR_BASE(fn) + SDIO_FBR_BLKSIZE, blksz & 0xff, NULL);
	if (ret)
		return ret;
	ret = SDIO_CMD52(1, 0, SDIO_FBR_BASE(fn) + SDIO_FBR_BLKSIZE + 1, (blksz >> 8) & 0xff, NULL);
    if (ret)
        return ret;

    sdiofunc[fn].cur_blksize = blksz;
	return ret;
#endif
#else
	if (blksz > 512u)
		return MMC_ERR_INVALIDARG;

	ret = SDIO_CMD52(1, 0, SDIO_FBR_BASE(fn) + SDIO_FBR_BLKSIZE, blksz & 0xff, NULL);
	if (ret)
		return ret;
	ret = SDIO_CMD52(1, 0, SDIO_FBR_BASE(fn) + SDIO_FBR_BLKSIZE + 1, (blksz >> 8) & 0xff, NULL);
    if (ret)
        return ret;

    sdiofunc[fn].cur_blksize = blksz;
	return ret;
#endif
}

/**
 *  @brief: This function set irq of function.
 *
 *  @num: number of function
 *  @return MLAN_STATUS_SUCCESS or MLAN_STATUS_FAILURE
 */
int sdio_set_function_irq(UINT8 num)
{
    int ret;
    UINT8 data;
    ret = SDIO_CMD52(0, 0, SDIO_CCCR_IENx, NULL, &data);
    if(ret)
    {
        CPUartLogPrintf("SDIOERR:Read func irq error\r\n");
        return ret;
    }

    data |= 1<<num;
    data |= 1;

    ret = SDIO_CMD52(1, 0, SDIO_CCCR_IENx, data, NULL);
    if(ret)
    {
        CPUartLogPrintf("SDIOERR:Set func irq error\r\n");
    }

    return ret;
}

static int cistpl_funce_common(UINT8 fn, const unsigned char *buf, unsigned size)
{
    unsigned blksize;
	if (size < 0x04 || buf[0] != 0)
		return MMC_ERR_INVALIDARG;

	/* TPLFE_FN0_BLK_SIZE */
	blksize = buf[1] | (buf[2] << 8);

    sdiofunc[fn].max_blksize = blksize;
    #if SDIO_DEBUG
	CPUartLogPrintf("SDIO:function %d maxblksize is %d\r\n", fn, blksize);
    #endif
	return 0;
}

static int cistpl_funce_func(UINT8 fn, const unsigned char *buf, unsigned size)
{
	unsigned vsn;
	unsigned min_size;
    unsigned max_blksize;

	vsn = sdiocccr.sdio_vsn;
	min_size = (vsn == SDIO_SDIO_REV_1_00) ? 28 : 42;

	if (size < min_size || buf[0] != 1)
		return MMC_ERR_INVALIDARG;

	/* TPLFE_MAX_BLK_SIZE */
	max_blksize = buf[12] | (buf[13] << 8);

    sdiofunc[fn].max_blksize = max_blksize;
    #if SDIO_DEBUG
    CPUartLogPrintf("SDIO:function %d maxblksize is %d\r\n", fn, max_blksize);
    #endif
	return 0;
}

int cistpl_funce(UINT8 fn, const unsigned char *buf, unsigned size)
{
	int ret;

	/*
	 * There should be two versions of the CISTPL_FUNCE tuple,
	 * one for the common CIS (function 0) and a version used by
	 * the individual function's CIS (1-7). Yet, the later has a
	 * different length depending on the SDIO spec version.
	 */
	if (fn)
		ret = cistpl_funce_func(fn, buf, size);
	else
		ret = cistpl_funce_common(fn, buf, size);

	if (ret)
    {
	    CPUartLogPrintf("SDIOERR:cistpl_funce error\r\n");
		return ret;
	}

	return 0;
}

int cistpl_manfid(UINT8 fn, const unsigned char *buf, unsigned size)
{
    unsigned int vendor, device;

	/* TPLMID_MANF */
	vendor = buf[0] | (buf[1] << 8);

	/* TPLMID_CARD */
	device = buf[2] | (buf[3] << 8);

    sdiofunc[fn].device = device;
    sdiofunc[fn].vendor = vendor;
#if 1
    CPUartLogPrintf("SDIO:VendorID:0x%x, DeviceID:0x%x\r\n", vendor, device);
#endif
    return 0;
}

int sdio_read_cis(UINT8 num)
{
    int ret;
	struct sdio_func_tuple *this = NULL;
	unsigned i, ptr = 0;

	/*
	 * Note that this works for the common CIS (function number 0) as
	 * well as a function's CIS * since SDIO_CCCR_CIS and SDIO_FBR_CIS
	 * have the same offset.
	 */
	for (i = 0; i < 3; i++) {
		unsigned char x, fn;

		if (num)
			fn = num;
		else
			fn = 0;

		ret = SDIO_CMD52(0, 0, SDIO_FBR_BASE(fn) + SDIO_FBR_CIS + i, 0, &x);
		if (ret)
			return ret;
		ptr |= x << (i * 8);
	}
#if SDIO_DEBUG
    CPUartLogPrintf("cis base addr = %x\r\n", ptr);
#endif
	if (ptr < SDIO_CIS_BASE)
		//something wrong, maybe HW
		return MMC_ERR_INVALID;

    do {
		unsigned char tpl_code, tpl_link;

		ret = SDIO_CMD52(0, 0, ptr++, 0, &tpl_code);
		if (ret)
			break;

		/* 0xff means we're done */
		if (tpl_code == 0xff)
			break;

		/* null entries have no link field or data */
		if (tpl_code == 0x00)
			continue;

		ret = SDIO_CMD52(0, 0, ptr++, 0, &tpl_link);
		if (ret)
			break;

		/* a size of 0xff also means we're done */
		if (tpl_link == 0xff)
			break;

        if(this != NULL)
        {
            free(this);
            this = NULL;
        }
		this = (struct sdio_func_tuple*)malloc(sizeof(*this) + tpl_link);
		if (!this)
		{
            ret = MMC_ERR_OUTMEMORY;
            this = NULL;
            break;
		}

		for (i = 0; i < tpl_link; i++) {
			ret = SDIO_CMD52(0, 0, ptr + i, 0, &this->data[i]);
			if (ret)
				break;
		}
		if (ret) {
			free(this);
            this = NULL;
			break;
		}

		for (i = 0; i < sizeof(cis_tpl_list)/sizeof(cis_tpl_list[0]); i++)
			if (cis_tpl_list[i].code == tpl_code)
				break;
		if (i >= sizeof(cis_tpl_list)/sizeof(cis_tpl_list[0]))
        {
			/* this tuple is unknown to the core */
			this->next = NULL;
			this->code = tpl_code;
			this->size = tpl_link;
		}
        else
		{
			const struct cis_tpl *tpl = cis_tpl_list + i;
			if (tpl_link < tpl->min_size)
            {

				ret = MMC_ERR_INVALID;
			}
            else if (tpl->parse)
            {
				ret = tpl->parse(num, this->data, tpl_link);
			}

			free(this);
            this = NULL;
		}

		ptr += tpl_link;
	} while (!ret);

    if(this != NULL)
    {
        free(this);
        this = NULL;
    }
    return ret;
}

/**
 *  @brief This function select function number according to device id and vendor id.
 *
 *  @num: pointer to number of function
 *  @return Success return 0, other return -1
 */
int sdio_select_function(int *num)
{
    int i = 0;
    int ret = 0;
    int fn_base = 0;
#ifndef SSVWIFI_FUNCTION
    if(GetWiFiType() != 3 && GetWiFiType() != 6) //for ASR SDU IP, tuple with id==0x20(which includes device id and vendor id) is defined in func0 CIS area
        fn_base = 1;
    CPUartLogPrintf("sdio_select_function: DEVICEID is 0x%x, funcnumber is %d fn_base %d\r\n",
        DEVICEID, funcnumber, fn_base);
    for(i=fn_base; i<=funcnumber; i++)
    {
        ret = sdio_read_cis(i);
        if(ret)
        {
            CPUartLogPrintf("sdio_select_function: sdiofunc[%d] sdio_read_cis failed, ret %dr\n", i, ret);
            goto Handle_Err;
        }
        CPUartLogPrintf("sdio_select_function: sdiofunc[%d].device is 0x%x, sdiofunc[%d].vendor is 0x%x\r\n",
            i, sdiofunc[i].device, i, sdiofunc[i].vendor);
        if(sdiofunc[i].device == DEVICEID)
        {
            if(sdiofunc[i].vendor == MRVL_VENDORID)
                *num = i;
            else// if(sdiofunc[i].vendor == HERON_VENDORID)
                *num = 1; //for heron SDU IP, i is 0, but real wifi function is func1
            return 0;
        }
    }
#else
	//sv wifi function is 1
	*num = 1;
	return 0;
#endif

Handle_Err:
    CPUartLogPrintf("SDIOERR:Select func error\r\n");
    return -1;
}

#ifdef QUECTEL_PROJECT_CUST
#ifdef AICWIFI_SUPPORT
int sdio_read_function(void)
{
    int i = 0;
    int ret = 0;
    int fn_base = 0;

    for(i=fn_base; i<=funcnumber; i++)
    {
        ret = sdio_read_cis(i);
        if(ret)
            goto Handle_Err;
        CPUartLogPrintf("sdio_select_function: sdiofunc[%d].device is 0x%x, sdiofunc[%d].vendor is 0x%x\r\n",
            i, sdiofunc[i].device, i, sdiofunc[i].vendor);
        sdiofunc[i].num = i;
    }

	return 0;
Handle_Err:
    CPUartLogPrintf("SDIOERR:Select func error\r\n");
    return -1;
}

struct sdio_func *sdio_get_func(void)
{
    return sdiofunc;
}

void sdio_work_lock(UNSIGNED_CHAR lock)
{
	sdio_wake_lock = lock;
}

void sdio_set_fnum(UNSIGNED_CHAR num)
{
	sdiofnnum = num;
}
#endif
#endif

/**
 *  @brief: This function enable or disable card interrupt.
 *
 *  @enable: 1 is enable, 0 is disable
 */
void sdio_card_int(UNSIGNED_INT enable)
{
    UINT16 isr;

    isr = sdio_readw(SD_CTRL_BASE_ADDR, SD_NORM_INTR_STS_INTR_EBLE_offset);
    if(enable)
        isr |= CARD_INT;
    else
        isr &= ~CARD_INT;

    sdio_writew(SD_CTRL_BASE_ADDR, SD_NORM_INTR_STS_EBLE_offset, isr);
    sdio_writew(SD_CTRL_BASE_ADDR, SD_NORM_INTR_STS_INTR_EBLE_offset, isr);
}

static void sdhci_dumpregs(UINT32 base)
{
	CPUartLogPrintf("============== REGISTER DUMP ==============\r\n");

	CPUartLogPrintf("SD_SYSADDR_LOW(0x%03x):0x%08x",SD_SYSADDR_LOW_offset,sdio_readw(base, SD_SYSADDR_LOW_offset));
	CPUartLogPrintf("SD_SYSADDR_HIGH(0x%03x):0x%08x",SD_SYSADDR_HIGH_offset,sdio_readw(base, SD_SYSADDR_HIGH_offset));
	CPUartLogPrintf("SD_BLOCK_SIZE(0x%03x):0x%08x",SD_BLOCK_SIZE_offset,sdio_readw(base, SD_BLOCK_SIZE_offset));
	CPUartLogPrintf("SD_BLOCK_COUNT(0x%03x):0x%08x",SD_BLOCK_COUNT_offset,sdio_readw(base, SD_BLOCK_COUNT_offset));
	CPUartLogPrintf("SD_ARG_LOW(0x%03x):0x%08x",SD_ARG_LOW_offset,sdio_readw(base, SD_ARG_LOW_offset));
	CPUartLogPrintf("SD_ARG_HIGH(0x%03x):0x%08x",SD_ARG_HIGH_offset,sdio_readw(base, SD_ARG_HIGH_offset));
	CPUartLogPrintf("SD_TRANSFER_MODE(0x%03x):0x%08x",SD_TRANSFER_MODE_offset,sdio_readw(base, SD_TRANSFER_MODE_offset));
	CPUartLogPrintf("SD_CMD(0x%03x):0x%08x",SD_CMD_offset,sdio_readw(base, SD_CMD_offset));

	CPUartLogPrintf("SD_RESP_0(0x%03x):0x%08x",SD_RESP_0_offset,sdio_readw(base, SD_RESP_0_offset));
	CPUartLogPrintf("SD_RESP_1(0x%03x):0x%08x",SD_RESP_1_offset,sdio_readw(base, SD_RESP_1_offset));
	CPUartLogPrintf("SD_RESP_2(0x%03x):0x%08x",SD_RESP_2_offset,sdio_readw(base, SD_RESP_2_offset));
	CPUartLogPrintf("SD_RESP_3(0x%03x):0x%08x",SD_RESP_3_offset,sdio_readw(base, SD_RESP_3_offset));
	CPUartLogPrintf("SD_RESP_4(0x%03x):0x%08x",SD_RESP_4_offset,sdio_readw(base, SD_RESP_4_offset));
	CPUartLogPrintf("SD_RESP_5(0x%03x):0x%08x",SD_RESP_5_offset,sdio_readw(base, SD_RESP_5_offset));
	CPUartLogPrintf("SD_RESP_6(0x%03x):0x%08x",SD_RESP_6_offset,sdio_readw(base, SD_RESP_6_offset));
	CPUartLogPrintf("SD_RESP_7(0x%03x):0x%08x",SD_RESP_7_offset,sdio_readw(base, SD_RESP_7_offset));

	CPUartLogPrintf("SD_BUF_PORT_0(0x%03x):0x%08x",SD_BUF_PORT_0_offset,sdio_readw(base, SD_BUF_PORT_0_offset));
	CPUartLogPrintf("SD_BUF_PORT_1(0x%03x):0x%08x",SD_BUF_PORT_1_offset,sdio_readw(base, SD_BUF_PORT_1_offset));
	CPUartLogPrintf("SD_PRESENT_STAT_0(0x%03x):0x%08x",SD_PRESENT_STAT_0_offset,sdio_readw(base, SD_PRESENT_STAT_0_offset));
	CPUartLogPrintf("SD_PRESENT_STAT_1(0x%03x):0x%08x",SD_PRESENT_STAT_1_offset,sdio_readw(base, SD_PRESENT_STAT_1_offset));
	CPUartLogPrintf("SD_HOST_CTRL(0x%03x):0x%08x",SD_HOST_CTRL_offset,sdio_readw(base, SD_HOST_CTRL_offset));
	CPUartLogPrintf("SD_BGAP_CTRL(0x%03x):0x%08x",SD_BGAP_CTRL_offset,sdio_readw(base, SD_BGAP_CTRL_offset));
	CPUartLogPrintf("SD_CLOCK_CTRL(0x%03x):0x%08x",SD_CLOCK_CTRL_offset,sdio_readw(base, SD_CLOCK_CTRL_offset));
	CPUartLogPrintf("SD_SW_RESET_CTRL(0x%03x):0x%08x",SD_SW_RESET_CTRL_offset,sdio_readw(base, SD_SW_RESET_CTRL_offset));

	CPUartLogPrintf("SD_NORM_INTR_STS(0x%03x):0x%08x",SD_NORM_INTR_STS_offset,sdio_readw(base, SD_NORM_INTR_STS_offset));
	CPUartLogPrintf("SD_ERR_INTR_STS(0x%03x):0x%08x",SD_ERR_INTR_STS_offset,sdio_readw(base, SD_ERR_INTR_STS_offset));
	CPUartLogPrintf("SD_NORM_INTR_STS_EBLE(0x%03x):0x%08x",SD_NORM_INTR_STS_EBLE_offset,sdio_readw(base, SD_NORM_INTR_STS_EBLE_offset));
	CPUartLogPrintf("SD_ERR_INTR_STS_EBLE(0x%03x):0x%08x",SD_ERR_INTR_STS_EBLE_offset,sdio_readw(base, SD_ERR_INTR_STS_EBLE_offset));
	CPUartLogPrintf("SD_NORM_INTR_STS_INTR_EBLE(0x%03x):0x%08x",SD_NORM_INTR_STS_INTR_EBLE_offset,sdio_readw(base, SD_NORM_INTR_STS_INTR_EBLE_offset));
	CPUartLogPrintf("SD_ERR_INTR_STS_INTR_EBLE0x%03x):0x%08x",SD_ERR_INTR_STS_INTR_EBLE_offset,sdio_readw(base, SD_ERR_INTR_STS_INTR_EBLE_offset));
	CPUartLogPrintf("SD_AUTO_CMD12_ERROR_STATUS(0x%03x):0x%08x",SDHCI_ACMD12_ERR,sdio_readw(base, SDHCI_ACMD12_ERR));
	CPUartLogPrintf("HOST_CTRL_2(0x%03x):0x%08x",SDHCI_HOST_CTRL2,sdio_readw(base, SDHCI_HOST_CTRL2));
	CPUartLogPrintf("SDHC_OP_CTRL(0x%03x):0x%08x",SD_CLOCK_AND_BURST_SIZE_SETUP_offset,sdio_readl(base, SD_CLOCK_AND_BURST_SIZE_SETUP_offset));
	CPUartLogPrintf("SDHC_OP_EXT_REG(0x%03x):0x%08x",0x108,sdio_readl(base, 0x108));
	CPUartLogPrintf("SDHCI_RX_CFG_REG(0x%03x):0x%08x",SDHCI_RX_CFG_REG,sdio_readl(base, SDHCI_RX_CFG_REG));
	CPUartLogPrintf("SDHCI_TX_CFG_REG(0x%03x):0x%08x",SDHCI_TX_CFG,sdio_readl(base, SDHCI_TX_CFG));
	CPUartLogPrintf("SDHC_DLINE_CTRL_REG(0x%03x):0x%08x",SDHC_DLINE_CTRL_REG,sdio_readl(base, SDHC_DLINE_CTRL_REG));
	CPUartLogPrintf("SDHC_DLINE_CFG_REG(0x%03x):0x%08x",SDHC_DLINE_CFG_REG,sdio_readl(base, SDHC_DLINE_CFG_REG));
	CPUartLogPrintf(": ===========================================\r\n");
}

/**
 *  @brief: This function handle HISR, mainly handle callback function
 *
 */
static void sdio_hisr(void)
{
    OS_STATUS os_status;
	UINT32 count=0;

#if SDIO_DEBUG
    CPUartLogPrintf("enter hisr!\r\n");
#endif
	if(DATA_COMP_status)
	{
	    DATA_COMP_status = 0;
	    OSASemaphorePoll(sdio_sem_data, &count);
	    if(count == 0)
	        OSASemaphoreRelease(sdio_sem_data);
	}

	if(sdio_cardint)
	{
		sdio_cardint=0;

		if (SDIOFlgRef)
	    	os_status = OSAFlagSet (SDIOFlgRef, SDIO_INTERRUPT_FLAG_BIT, OSA_FLAG_OR);
	}

#if SDIO_DEBUG
    CPUartLogPrintf("leave hisr!\r\n");
#endif
}

static void sdio_semhisr(void)
{
    unsigned long count = 1;

    DATA_COMP_status = 0;
    OSASemaphorePoll(sdio_sem_data, &count);
    if(count == 0)
        OSASemaphoreRelease(sdio_sem_data);

}


/**
 *  @brief: This function handle LISR.
 *
 */
void sdio_isr(INTC_InterruptInfo sourceInfo)
{
	volatile INT16 isr_sts = 0;
    volatile INT16 err_sts = 0;

    cmderror = 0;

#if SDIO_DEBUG
    CPUartLogPrintf("enter sdio_isr\r\n");
#endif

    isr_sts = sdio_readw(SD_CTRL_BASE_ADDR, SD_NORM_INTR_STS_offset);
    if (isr_sts == 0 || isr_sts == 0xFFFF)
    {
        CPUartLogPrintf("SDIOERR:isr sts:%x\r\n", isr_sts);
		return;
	}

    intstsrec = isr_sts;

    sdio_writew(SD_CTRL_BASE_ADDR, SD_NORM_INTR_STS_offset, isr_sts);

#if SDIO_DEBUG
    CPUartLogPrintf("NORM_INTR_STS=%x\r\n", isr_sts);
#endif

    if (isr_sts & (CARD_REM | CARD_INS))
    {
        CPUartLogPrintf("SDIOERR:CARD Remove/Insert: %x\r\n", isr_sts);
    }

    if (isr_sts & (CMD_COMP))
    {
        CMD_COMP_status = 1;
#if SDIO_DEBUG
        CPUartLogPrintf("Command complete");
#endif
    }

    if (isr_sts & (XFER_COMP))
    {
        DATA_COMP_status = 1;

#if SDIO_DEBUG
        CPUartLogPrintf("Data transfer complete");
#endif
    }

	if (isr_sts & DMA_INT)
	{
		sdio_writel(SD_CTRL_BASE_ADDR, SD_SYSADDR_LOW_offset, sdio_readl(SD_CTRL_BASE_ADDR, SD_SYSADDR_LOW_offset));
	}

    if (isr_sts & CARD_INT)
    {
		sdio_cardint=1;
		//CPUartLogPrintf("CARD_INT");
        /* For debug*/
        sdio_card_int(0);
    }

	if (isr_sts & ERR_INT )
	{
        err_sts = sdio_readw(SD_CTRL_BASE_ADDR, SD_ERR_INTR_STS_offset);
        sdio_writew(SD_CTRL_BASE_ADDR, SD_ERR_INTR_STS_offset, err_sts);

        CPUartLogPrintf("SDIOERR:err_sts = %x\r\n", err_sts);
        errintstsrec = err_sts;

        if (err_sts & (CMD_TO_ERR | CMD_IDX_ERR | CMD_END_BIT_ERR | CMD_CRC_ERR))
        {
    		if (err_sts & CMD_TO_ERR)
    		{
                CPUartLogPrintf("SDIOERR:Command timeout!%d,%d\r\n",CMD_COMP_status,DATA_COMP_status);
    	    }
    		if (err_sts & CMD_IDX_ERR)
    		{
                CPUartLogPrintf("SDIOERR:Command index error!\r\n");
            }
    		if (err_sts & CMD_END_BIT_ERR)
    		{
                CPUartLogPrintf("SDIOERR:Command end bit error!\r\n");
    		}
    		if (err_sts & CMD_CRC_ERR)
    		{
                CPUartLogPrintf("SDIOERR:Command CRC error!\r\n");
    		}

        }

        if (err_sts & (DATA_TO_ERR | RD_DATA_CRC_ERR | RD_DATA_END_ERR))
        {
    		if (err_sts & DATA_TO_ERR)
            {
                CPUartLogPrintf("SDIOERR:Data timeout!%d,%d\r\n",CMD_COMP_status,DATA_COMP_status);
    	    }
    		if (err_sts & RD_DATA_CRC_ERR)
    		{
                CPUartLogPrintf("SDIOERR:Read data CRC error!\r\n");
    		}
    		if (err_sts & RD_DATA_END_ERR)
    		{
                CPUartLogPrintf("SDIOERR:Read data end bit error!\r\n");
    		}

        }

		if (err_sts & CUR_LIMIT_ERR)
			CPUartLogPrintf("SDIOERR:Current limit error!\r\n");
		if (err_sts & AUTO_CMD12_ERR)
			CPUartLogPrintf("SDIOERR:Auto CMD12 error!\r\n");
		if (err_sts & SPI_ERR)
			CPUartLogPrintf("SDIOERR:SPI error!\r\n");
		if (err_sts & AXI_RESP_ERR)
			CPUartLogPrintf("SDIOERR:AXI Bus error!\r\n");
		if (err_sts & CPL_TO_ERR)
			CPUartLogPrintf("SDIOERR:CE-ATA error!\r\n");
		if (err_sts & ADMA_ERR)
			CPUartLogPrintf("SDIOERR:ADMA error!\r\n");
		if (err_sts & CRC_STATUS_ERR)
			CPUartLogPrintf("SDIOERR:CRC status error!\r\n");

        cmderror = 1;
        sdio_sw_reset(SD_CTRL_BASE_ADDR);
	}

    if(sdio_cardint || DATA_COMP_status)
    {
        OS_Activate_HISR(&SdioHISR);
    }
#if SDIO_DEBUG
    CPUartLogPrintf("leave sdio_isr\r\n");
#endif
}

static void asr_sw_rx_tuning_prepare(UINT32 base_addr, UINT8 dline_reg)
{
	UINT32 reg = sdio_readl(base_addr, SDHC_DLINE_CFG_REG);
    CPUartLogPrintf("asr_sw_rx_tuning_prepare enter: base_addr=0x%x dline_reg=%d", base_addr, dline_reg);
	reg &= ~(RX_DLINE_REG_MASK << RX_DLINE_REG_SHIFT);
	reg |= dline_reg << RX_DLINE_REG_SHIFT;
	/* release RX reset signal */
	reg |= 0x1 << RX_DLINE_RSTB_SHIFT;
	sdio_writel(base_addr, SDHC_DLINE_CFG_REG, reg);

	reg = sdio_readl(base_addr, SDHC_DLINE_CTRL_REG);
	reg |= DLINE_PU;
	sdio_writel(base_addr, SDHC_DLINE_CTRL_REG, reg);

	reg = sdio_readl(base_addr, SDHCI_RX_CFG_REG);
	reg &= ~(RX_SDCLK_SEL1_MASK << RX_SDCLK_SEL1_SHIFT);
	reg |= RX_SDCLK_SEL1_DDLL << RX_SDCLK_SEL1_SHIFT;
	sdio_writel(base_addr, SDHCI_RX_CFG_REG, reg);
    CPUartLogPrintf("asr_sw_rx_tuning_prepare exit: read SDHC_DLINE_CTRL_REG(0x%x)=0x%lx SDHC_DLINE_CFG_REG(0x%x)=0x%lx SDHCI_RX_CFG_REG(0x%x)=0x%lx",
        SDHC_DLINE_CTRL_REG, sdio_readl(base_addr, SDHC_DLINE_CTRL_REG),
        SDHC_DLINE_CFG_REG, sdio_readl(base_addr, SDHC_DLINE_CFG_REG),
        SDHCI_RX_CFG_REG, sdio_readl(base_addr, SDHCI_RX_CFG_REG));
}

static void asr_sdhci_clear_set_irqs(UINT32 base_addr, UINT32 clr, UINT32 set)
{
	UINT32 ier = sdio_readl(base_addr, SD_NORM_INTR_STS_EBLE_offset);
	ier &= ~clr;
	ier |= set;
	sdio_writel(base_addr, SD_NORM_INTR_STS_EBLE_offset, ier);
	sdio_writel(base_addr, SD_NORM_INTR_STS_INTR_EBLE_offset, ier);
}

void asr_reset_tuning(void)
{
	UINT16 ctrl = sdio_readw(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2);
	ctrl &= ~(SDHCI_CTRL_TUNED_CLK|SDHCI_CTRL_EXEC_TUNING);
	sdio_writew(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2, ctrl);
}

static void asr_sw_rx_set_delaycode(UINT32 base_addr, UINT32 delay)
{
	UINT32 reg = sdio_readl(base_addr, SDHC_DLINE_CTRL_REG);
	reg &= ~(RX_DLINE_CODE_MASK << RX_DLINE_CODE_SHIFT);
	reg |= (delay & RX_DLINE_CODE_MASK) << RX_DLINE_CODE_SHIFT;
	sdio_writel(base_addr, SDHC_DLINE_CTRL_REG, reg);
    //CPUartLogPrintf("asr_sw_rx_set_delaycode: write SDHC_DLINE_CTRL_REG(0x%x)=0x%lx delay=%d",
    //    SDHC_DLINE_CTRL_REG, sdio_readl(base_addr, SDHC_DLINE_CTRL_REG), delay);
}

static void asr_prepare_tuning(UINT32 base_addr, UINT32 timing, UINT32 val, BOOL done)
{
	//CPUartLogPrintf("asr_prepare_tuning enter: done=%d tunning with delay %d *rx_delay %d\n", done, val, *rx_delay);
	//if (timing <= MMC_TIMING_MMC_HS400 && done) {
	//    *rx_delay = val;
	//}
	asr_sw_rx_set_delaycode(base_addr, val);
	//CPUartLogPrintf("asr_prepare_tuning: done=%d tunning with delay %d *rx_delay %d\n", done, val, *rx_delay);
}

static const UINT32 tuning_patten4[16] = {
	0x00ff0fff, 0xccc3ccff, 0xffcc3cc3, 0xeffefffe,
	0xddffdfff, 0xfbfffbff, 0xff7fffbf, 0xefbdf777,
	0xf0fff0ff, 0x3cccfc0f, 0xcfcc33cc, 0xeeffefff,
	0xfdfffdff, 0xffbfffdf, 0xfff7ffbb, 0xde7b7ff7,
};

/*
 * return 0: sucess, >=1: the num of pattern check errors
 */
static int asr_tuning_pio_check(UINT32 base_addr, int point)
{
	UINT32 rd_patten;
	unsigned int i;
	UINT32 *tuning_patten;
	int patten_len = 16;
	int err = 0;

	tuning_patten = (UINT32 *)tuning_patten4;

	/* read all the data from FIFO, avoid error if IC design is not good */
	for (i = 0; i < patten_len; i++) {
		rd_patten = sdio_readl(base_addr, SD_BUF_PORT_0_offset);
		if (rd_patten != tuning_patten[i])
        {
			err++;
	        //CPUartLogPrintf("asr_tuning_pio_check err: no%d rd_patten=0x%x not equal to tuning_patten[%d]=0x%x SDHC_DLINE_CTRL_REG(0x%x)=0x%x\n",
            //    i, rd_patten, i, tuning_patten[i], SDHC_DLINE_CTRL_REG, sdio_readl(base_addr, SDHC_DLINE_CTRL_REG));
        }
	}
    //if(err)
	//    CPUartLogPrintf("asr_tuning_pio_check exit: point: %d, error: %d\n", point, err);
	return err;
}

#define SDHC_CMD_DATA_READ   (1<<4)
#define SDHC_CMD_CHECK_CRC	 (1<<19)
#define SDHC_CMD_CHECK_INDEX (1<<20)
#define SDHC_CMD_DATA_PRESENT (1<<21)
#define SDHC_RES_48BIT		 (2<<16)
UINT32 sdhc_check_response (UINT32 cmd, UINT32 resp)
{
  UINT32 data;
  data = ((cmd) & 0x3f) << 24;
  data |= (resp | SDHC_CMD_CHECK_CRC | SDHC_CMD_CHECK_INDEX);
  return data;
}

static int asr_send_tuning_cmd(UINT32 base_addr, UINT32 opcode, int point, unsigned long flags)
{
	int err = 0, err_int = 0;
    UINT32 osaWaitflag = 0;
    UINT32 cmd = 0x133a, data; //CMD19
    UINT32 mask;
	volatile int timeout = 0;

    //set timeout
    sdio_writeb(base_addr, SD_SW_RESET_CTRL_offset, 0x0E);

    data = sdio_readw(base_addr, SDHCI_ACMD12_ERR);
    data |= 0x1f; //clear auto cmd error
    sdio_writew(base_addr, SDHCI_ACMD12_ERR, data);
    sdio_writel(base_addr, SD_NORM_INTR_STS_offset, 0xffffffff);

    cmd = sdhc_check_response (opcode, SDHC_RES_48BIT);
    cmd |= SDHC_CMD_DATA_PRESENT | SDHC_CMD_DATA_READ;

	sdio_writel(base_addr, SD_ARG_LOW_offset, 0);
	//sdio_writew(base_addr, SD_BLOCK_SIZE_offset, SDHCI_MAKE_BLKSZ(7, 64));
	sdio_writel(base_addr, SD_BLOCK_SIZE_offset, (0x10000 | 64 | (7<<12)));

	/*
	 * The tuning block is sent by the card to the host controller.
	 * So we set the TRNS_READ bit in the Transfer Mode register.
	 * This also takes care of setting DMA Enable and Multi Block
	 * Select in the same register to 0.
	 */
	//sdio_writew(base_addr, SD_TRANSFER_MODE_offset, SDHCI_TRNS_READ);

	sdio_writel(base_addr, SD_TRANSFER_MODE_offset, cmd);
    //CPUartLogPrintf("asr_send_tuning_cmd: base_addr=0x%x opcode=%d point=%d flags=%d", base_addr, opcode, point, flags);
    //CPUartLogPrintf("asr_send_tuning_cmd: read SD_TRANSFER_MODE_offset(0x%x)=0x%lx SD_ARG_LOW_offset(0x%x)=0x%lx SDHCI_ACMD12_ERR(0x%x)=0x%lx",
    //    SD_TRANSFER_MODE_offset, sdio_readl(base_addr, SD_TRANSFER_MODE_offset),
    //    SD_ARG_LOW_offset, sdio_readl(base_addr, SD_ARG_LOW_offset),
    //    SDHCI_ACMD12_ERR, sdio_readw(base_addr, SDHCI_ACMD12_ERR));
    timeout = 1000;
    while (1)
    {
        data = sdio_readw(base_addr, SD_NORM_INTR_STS_offset);
        if (data & ERR_INT)
        {
            UINT32 val = sdio_readw(base_addr, SDHCI_ACMD12_ERR);
            sdio_mswait(1);
            //CPUartLogPrintf("asr_send_tuning_cmd err int: delay=%d read SD_NORM_INTR_STS_offset(0x%x)=0x%x",
            //    point, SD_NORM_INTR_STS_offset, sdio_readl(base_addr, SD_NORM_INTR_STS_offset));
            val |= 0x1f; //clear auto cmd error
            sdio_writew(base_addr, SDHCI_ACMD12_ERR, val);
            sdio_writel(base_addr, SD_NORM_INTR_STS_offset, 0xffffffff);
            err_int = 1;
        }
        if (data & RX_RDY)
        {
            //CPUartLogPrintf("asr_send_tuning_cmd: RX_RDY int!");
            sdio_writew(base_addr, SD_NORM_INTR_STS_offset, data);
        	err = asr_tuning_pio_check(base_addr, point);
            break;
        }
        timeout--;
        sdio_mswait(1);
        if(timeout == 0)
        {
            CPUartLogPrintf("asr_send_tuning_cmd: waiting tuning_done timeout");
            ASSERT(0);
            /* clean all interrupt bits */
            data = sdio_readw(base_addr, SDHCI_ACMD12_ERR);
            data |= 0x1f; //clear auto cmd error
            sdio_writew(base_addr, SDHCI_ACMD12_ERR, data);
            sdio_writel(base_addr, SD_NORM_INTR_STS_offset, 0xffffffff);
            return -1;
        }
    }
    if(err_int)
        sdio_sw_reset(base_addr);
	return err;
}

static int asr_set_rx_timing_cfg(UINT32 base_addr, UINT32 timing, UINT32 rx_delay, UINT32 rx_dline_reg)
{
	if (timing > MMC_TIMING_MMC_HS400) {
		CPUartLogPrintf("%s: invalid timing %d\n", __func__, timing);
		return 0;
	}
	//asr_sw_rx_tuning_prepare(base_addr, rx_dline_reg);
	asr_sw_rx_set_delaycode(base_addr, rx_delay);
	CPUartLogPrintf("asr_set_rx_timing_cfg: timing=%d rx_delay_code=%d rx_dline_reg=%d\n", timing, rx_delay, rx_dline_reg);
	return 1;
}

//static
int asr_execute_tuning(UINT32 timing/*, UINT32 *rx_delay, UINT32 rx_dline_reg*/)
{
    UINT32 rx_dline_reg = 0;
    UINT32 base_addr = SD_CTRL_BASE_ADDR;
    UINT32 opcode = MMC_SEND_TUNING_BLOCK;
	int min, max = 0, ret;
	int len = 0, avg = 0;
	unsigned long flags = 0;
	UINT32 ier = sdio_readl(base_addr, SD_NORM_INTR_STS_EBLE_offset);

    //CPUartLogPrintf("asr_execute_tuning enter: base_addr=0x%x timing=%d ier=0x%x", base_addr, timing, ier);

	if (timing <= MMC_TIMING_MMC_HS400) {
		asr_sw_rx_tuning_prepare(base_addr, rx_dline_reg);
	}

	/* change to pio mode during the tuning stage */
	//asr_sdhci_clear_set_irqs(base_addr, ier, RX_RDY|ERR_INT);
	sdio_writel(base_addr, SD_NORM_INTR_STS_EBLE_offset, ier|RX_RDY);
	sdio_writel(base_addr, SD_NORM_INTR_STS_INTR_EBLE_offset, 0);
    //CPUartLogPrintf("asr_sw_rx_tuning_prepare: before tuning read SD_HOST_CTRL_offset(0x%x)=0x%x SDHCI_ACMD12_ERR(0x%x)=0x%x SDHCI_HOST_CTRL2(0x%x)=0x%x",
    //    SD_HOST_CTRL_offset, sdio_readl(base_addr, SD_HOST_CTRL_offset), SDHCI_ACMD12_ERR, sdio_readl(base_addr, SDHCI_ACMD12_ERR),
    //    SDHCI_HOST_CTRL2, sdio_readw(base_addr, SDHCI_HOST_CTRL2));

	/* find the mininum delay first which can pass tuning */
	min = SDHC_RX_TUNE_DELAY_MIN;
	do {
        //CPUartLogPrintf("asr_execute_tuning: first min=%d max=%d len=%d avg=%d", min, max, len, avg);
		while (min < SDHC_RX_TUNE_DELAY_MAX) {
            //sdhci_sv_dumpregs(base_addr);
			asr_prepare_tuning(base_addr, timing, min, FALSE);
			if (!asr_send_tuning_cmd(base_addr, opcode, min, flags))
				break;
            //sdhci_sv_dumpregs(base_addr);
			min += SDHC_RX_TUNE_DELAY_STEP;
		}

		/* find the maxinum delay which can not pass tuning */
		max = min + SDHC_RX_TUNE_DELAY_STEP;
        //CPUartLogPrintf("asr_execute_tuning: second min=%d max=%d len=%d avg=%d", min, max, len, avg);
		while (max < SDHC_RX_TUNE_DELAY_MAX) {
            //sdhci_sv_dumpregs(base_addr);
			asr_prepare_tuning(base_addr, timing, max, FALSE);
			if (asr_send_tuning_cmd(base_addr, opcode, max, flags))
				break;
            //sdhci_sv_dumpregs(base_addr);
			max += SDHC_RX_TUNE_DELAY_STEP;
		}

		if ((max - min) > len) {
			len = max - min;
			avg = (min + max - 1) / 2;
		}
        //CPUartLogPrintf("asr_execute_tuning: third min=%d max=%d len=%d avg=%d", min, max, len, avg);
		min = max + SDHC_RX_TUNE_DELAY_STEP;
	} while (min < SDHC_RX_TUNE_DELAY_MAX);

	asr_prepare_tuning(base_addr, timing, avg, TRUE);
	ret = asr_send_tuning_cmd(base_addr, opcode, avg, flags);
	//asr_sdhci_clear_set_irqs(base_addr, RX_RDY, ier);
	sdio_writel(base_addr, SD_NORM_INTR_STS_EBLE_offset, ier);
	sdio_writel(base_addr, SD_NORM_INTR_STS_INTR_EBLE_offset, ier);

	if (ret)
    {
		CPUartLogPrintf("asr_execute_tuning: tunning failed at %d, pass window length is %d", avg, len);
    }
	else
    {
		CPUartLogPrintf("asr_execute_tuning: tunning passed at %d, pass window length is %d", avg, len);
    }
    asr_set_rx_timing_cfg(SD_CTRL_BASE_ADDR, timing, avg, rx_dline_reg);
	return ret;
}

static int sdio_select_drive_strength()
{
    return 0;
}

static UINT8 host_drive_to_sdio_drive(int host_strength)
{
    switch(host_strength)
    {
    case MMC_SET_DRIVER_TYPE_A:
        return SDIO_DTSx_SET_TYPE_A;
    case MMC_SET_DRIVER_TYPE_B:
        return SDIO_DTSx_SET_TYPE_B;
    case MMC_SET_DRIVER_TYPE_C:
        return SDIO_DTSx_SET_TYPE_C;
    case MMC_SET_DRIVER_TYPE_D:
        return SDIO_DTSx_SET_TYPE_D;
    default:
        return SDIO_DTSx_SET_TYPE_B;
    }
}

static void sdio_select_driver_type()
{
    int host_drv_type = SD_DRIVER_TYPE_B;
    int card_drv_type = SD_DRIVER_TYPE_B;
    int drive_strength;
    UINT8 card_strength;
    int err;

    if(!(sdiocapa3 & (SD_DRIVER_A|SD_DRIVER_C|SD_DRIVER_D)))
        return;

    if(sdiocapa3 & SD_DRIVER_A)
        host_drv_type |= SD_DRIVER_TYPE_A;

    if(sdiocapa3 & SD_DRIVER_C)
        host_drv_type |= SD_DRIVER_TYPE_C;

    if(sdiocapa3 & SD_DRIVER_D)
        host_drv_type |= SD_DRIVER_TYPE_D;

    if(sd3_drv_type & SD_DRIVER_TYPE_A)
        card_drv_type |= SD_DRIVER_TYPE_A;

    if(sd3_drv_type & SD_DRIVER_TYPE_C)
        card_drv_type |= SD_DRIVER_TYPE_C;

    if(sd3_drv_type & SD_DRIVER_TYPE_D)
        card_drv_type |= SD_DRIVER_TYPE_D;

    drive_strength = sdio_select_drive_strength();

    err = SDIO_CMD52(0, 0, SDIO_CCCR_DRIVE_STRENGTH, 0, &card_strength);
    if(err)
        return;

    card_strength &= ~(SDIO_DRIVE_DTSx_MASK<<SDIO_DRIVE_DTSx_SHIFT);
    card_strength |= host_drive_to_sdio_drive(drive_strength);

    err = SDIO_CMD52(1, 0, SDIO_CCCR_DRIVE_STRENGTH, card_strength, 0);

}

void sdhci_set_uhs_signaling(UINT32 base_addr, unsigned timing)
{
	UINT16 ctrl_2;

	ctrl_2 = sdio_readw(base_addr, SDHCI_HOST_CTRL2);
	/* Select Bus Speed Mode for host */
	ctrl_2 &= ~SDHCI_CTRL_UHS_MASK;
	if ((timing == MMC_TIMING_MMC_HS200) ||
	    (timing == MMC_TIMING_UHS_SDR104))
		ctrl_2 |= SDHCI_CTRL_UHS_SDR104;
	else if (timing == MMC_TIMING_UHS_SDR12)
		ctrl_2 |= SDHCI_CTRL_UHS_SDR12;
	else if (timing == MMC_TIMING_UHS_SDR25)
		ctrl_2 |= SDHCI_CTRL_UHS_SDR25;
	else if (timing == MMC_TIMING_UHS_SDR50)
		ctrl_2 |= SDHCI_CTRL_UHS_SDR50;
	else if (timing == MMC_TIMING_UHS_DDR50)
		ctrl_2 |= SDHCI_CTRL_UHS_DDR50;
    ctrl_2 |= 0x8; //1.8V signaling enable
    CPUartLogPrintf("sdhci_set_uhs_signaling: set SDHCI_HOST_CTRL2(0x%x) to 0x%x", SDHCI_HOST_CTRL2, ctrl_2);
	sdio_writew(base_addr, SDHCI_HOST_CTRL2, ctrl_2);
}

static void sdio_tx_out_clk_sel()
{
	UINT32 base_addr = SD_CTRL_BASE_ADDR;
	UINT32 ctrl = sdio_readl(base_addr, SDHCI_TX_CFG);
	ctrl |= (1<<30);
	sdio_writel(base_addr,SDHCI_TX_CFG,ctrl);
	CPUartLogPrintf("SDIO:SDHCI_TX_CFG=0x%x", sdio_readl(base_addr,SDHCI_TX_CFG));
}

static void sdio_tx_no_tuning(void)
{
	UINT32 base_addr = SD_CTRL_BASE_ADDR;
	UINT32 ctrl = sdio_readl(base_addr, SDHCI_TX_CFG);
	ctrl &= ~(1<<31);
	sdio_writel(base_addr,SDHCI_TX_CFG,ctrl);
	//CPUartLogPrintf("sdio_tx_no_tuning: SDHCI_TX_CFG=0x%x", sdio_readl(base_addr,SDHCI_TX_CFG));
}

int sdio_uhs_set_clk(UINT8 timing, UINT32 clock)
{
    UINT32 sdh_clk_array[8] = {416000000, 350000000, 312000000, 0, 223000000, 107000000, 355000000, 189000000};
    UINT8 sdh_clk_sel, sdh_clk_div;
    UINT32 max_clk = UHS_SDR25_MAX_DTR, clk_src;
	int div = 0;
    volatile UINT32* sdh1_pmu_clk_reg = (volatile UINT32*)SD1_HOST_PMU_AXI_CLOCK;

    if(clock == 156000000) //Use clock source1 624MHz
    {
        *sdh1_pmu_clk_reg = 0x952; //SDH1_CLK_SEL bit[7:5]=3b'010, 312MHz. SDH1_CLK_DIV bit[10:8]=3b'001, div=1+1=2.
	    sdio_mswait(10);
        CPUartLogPrintf("sdio_uhs_set_clk special clock: timing=%d clock=%d set sdh1_pmu_clk_reg(0x%x)=0x%x",
            timing, clock, sdh1_pmu_clk_reg, *sdh1_pmu_clk_reg);
    }
    sdh_clk_sel = ((*((UINT32*)SD1_HOST_PMU_AXI_CLOCK))>>5)&0x7;
    sdh_clk_div = ((*((UINT32*)SD1_HOST_PMU_AXI_CLOCK))>>8)&0x7;
    clk_src = sdh_clk_array[sdh_clk_sel] / (sdh_clk_div+1);
    switch(timing) {
        case MMC_TIMING_UHS_SDR104:
            max_clk = UHS_SDR104_MAX_DTR;
            break;
        case MMC_TIMING_UHS_SDR50:
            max_clk = UHS_SDR50_MAX_DTR;
            break;
        case MMC_TIMING_UHS_DDR50:
            max_clk = UHS_DDR50_MAX_DTR;
            break;
        default:
        	CPUartLogPrintf("sdio_uhs_set_clk error: invalid timing=%d clock=%d", timing, clock);
            ASSERT(0);
            break;
    }
    if(clock == 156000000)
        max_clk = 156000000;
    if(clock > max_clk)
        clock = max_clk;
    if(clk_src < clock)
    {
    	CPUartLogPrintf("sdio_uhs_set_clk error: clk_src=%ld less than clock=%ld! timing=%d sdh(sel=%d div=%d clk_src=%ld), calculated div=%d",
            clk_src, clock, timing, sdh_clk_sel, sdh_clk_div, clk_src);
        ASSERT(0);
        return -1;
    }
    if(clock >= 104000000)
    {
        unsigned int mfpr_val = 0x40;
        volatile unsigned long *r;
        //config sdio
        r = (volatile unsigned long*)(0xd401E300);  //clk
        *r = mfpr_val|0xA000; //pull-dn
        CPUartLogPrintf("sdio_uhs_set_clk: read MMC1_CLK MFPR(0x%x)=0x%x", r, *r);
        //RTI_LOG("sdio_uhs_set_clk: read MMC1_CLK MFPR(0x%x)=0x%x", r, *r);

        r = (volatile unsigned long*)(0xd401E2FC);  //cmd
        *r = mfpr_val|0xC000; //pull-up
        CPUartLogPrintf("sdio_uhs_set_clk: read MMC1_CMD MFPR(0x%x)=0x%x", r, *r);
        //RTI_LOG("sdio_uhs_set_clk: read MMC1_CMD MFPR(0x%x)=0x%x", r, *r);

        r = (volatile unsigned long*)(0xd401E2F8);  //data0
        *r = mfpr_val|0xC000;
        CPUartLogPrintf("sdio_uhs_set_clk: read MMC1_DATA0 MFPR(0x%x)=0x%x", r, *r);
        //RTI_LOG("sdio_uhs_set_clk: read MMC1_DATA0 MFPR(0x%x)=0x%x", r, *r);

        r = (volatile unsigned long*)(0xd401E2F4);  //data1
        *r = mfpr_val|0xC000;
        CPUartLogPrintf("sdio_uhs_set_clk: read MMC1_DATA1 MFPR(0x%x)=0x%x", r, *r);
        //RTI_LOG("sdio_uhs_set_clk: read MMC1_DATA1 MFPR(0x%x)=0x%x", r, *r);

        r = (volatile unsigned long*)(0xd401E2F0);  //data2
        *r = mfpr_val|0xC000;
        CPUartLogPrintf("sdio_uhs_set_clk: read MMC1_DATA2 MFPR(0x%x)=0x%x", r, *r);
        //RTI_LOG("sdio_uhs_set_clk: read MMC1_DATA2 MFPR(0x%x)=0x%x", r, *r);

        r = (volatile unsigned long*)(0xd401E2EC);  //data3
        *r = mfpr_val|0xC000;
        CPUartLogPrintf("sdio_uhs_set_clk: read MMC1_DATA3 MFPR(0x%x)=0x%x", r, *r);
        //RTI_LOG("sdio_uhs_set_clk: read MMC1_DATA3 MFPR(0x%x)=0x%x", r, *r);
    }
	/* Version 3.00 divisors must be a multiple of 2. */
	if (clk_src <= clock)
		div = 1;
	else {
		for (div = 2; div < SDHCI_MAX_DIV_SPEC_300;
		     div += 2) {
			if ((clk_src / div) <= clock)
				break;
		}
	}
	div >>= 1;
	CPUartLogPrintf("sdio_uhs_set_clk: timing=%d max_clk=%d input clock=%ld sdh(sel=%d div=%d clk_src=%ld), calculated div=%d",
        timing, max_clk, clock, sdh_clk_sel, sdh_clk_div, clk_src, div);
	sdio_tx_out_clk_sel();
	sdio_tx_no_tuning();
    sdio_enable_clock(SD_CTRL_BASE_ADDR, div);
    return 0;
}

static int sdio_set_bus_speed_mode(UINT32 *timing, UINT32 *uhs_max_dtr)
{
    int err;
    UINT8 speed = 0, data = 0;
    UINT32 bus_speed;

    if(!(sdiocapa3 & (SDR104_SUPPORT|SDR50_SUPPORT|DDR50_SUPPORT)))
        return 0;

    CPUartLogPrintf("sdio_set_bus_speed_mode: sdiocapa3:0x%x, sd3_bus_mode:0x%x", sdiocapa3, sd3_bus_mode);

    bus_speed = SDIO_SPEED_SDR12;
    *timing = MMC_TIMING_UHS_SDR12;
	if ((sdiocapa3 & SDR104_SUPPORT) &&
	    (sd3_bus_mode & SD_MODE_UHS_SDR104))
    {
		bus_speed = SDIO_SPEED_SDR104;
		*timing = MMC_TIMING_UHS_SDR104;
		*uhs_max_dtr = UHS_SDR104_MAX_DTR;
        CPUartLogPrintf("SDIO_SPEED_SDR104");
	}
    else if ((sdiocapa3 & DDR50_SUPPORT) && (sd3_bus_mode & SD_MODE_UHS_DDR50))
    {
		bus_speed = SDIO_SPEED_DDR50;
		*timing = MMC_TIMING_UHS_DDR50;
		*uhs_max_dtr = UHS_DDR50_MAX_DTR;
        CPUartLogPrintf("SDIO_SPEED_DDR50");
	}
    else if ((sdiocapa3 & (SDR104_SUPPORT |
		    SDR50_SUPPORT)) && (sd3_bus_mode &
		    SD_MODE_UHS_SDR50))
    {
		bus_speed = SDIO_SPEED_SDR50;
		*timing = MMC_TIMING_UHS_SDR50;
		*uhs_max_dtr = UHS_SDR50_MAX_DTR;
        CPUartLogPrintf("SDIO_SPEED_SDR50");
	}
    else
    {
        CPUartLogPrintf("sdio_set_bus_speed_mode set 52Mhz");
		bus_speed = SDIO_SPEED_SDR50;
		*timing = MMC_TIMING_UHS_SDR50;
		*uhs_max_dtr = UHS_SDR50_MAX_DTR;
    }

	err = SDIO_CMD52(0, 0, SDIO_CCCR_SPEED, 0, &speed);
	if (err)
		return err;

	speed &= ~SDIO_SPEED_BSS_MASK;
	speed |= bus_speed;
	err = SDIO_CMD52(1, 0, SDIO_CCCR_SPEED, speed, 0);
	if (err)
		return err;
    if(GetWiFiType() == 6) {
        data = sdio_readb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset);
        //enable host ctrl
        data |= SDHCI_CTRL_HISPD;
        sdio_writeb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset, data);
        sdhci_set_uhs_signaling(SD_CTRL_BASE_ADDR, *timing);
        *uhs_max_dtr = 52000000; //HoopoeL bootrom can only support maximum 52MHz sdio clock
        sdio_uhs_set_clk(*timing, *uhs_max_dtr);
    }
    else {
    	if (bus_speed == SDIO_SPEED_SDR50) {
            UINT16 ctrl = 0;
            //uhs mode
            ctrl = sdio_readw(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2);
            CPUartLogPrintf("%s,SDHCI_HOST_CTRL2:0x%x", __FUNCTION__, ctrl);
            ctrl |= 0xA;
            sdio_writew(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2, ctrl);

            sdio_enable_clock(SD_CTRL_BASE_ADDR, 1); //104Mhz clock
    	}
        else
        {
            CPUartLogPrintf("bus_speed:%x", bus_speed);
        }
    }
    CPUartLogPrintf("sdio_set_bus_speed_mode exit: err %d timing %d uhs_max_dtr %d", err, *timing, *uhs_max_dtr);
	return err;
}

static int sdio_init_uhs_card()
{
    int err;
    UINT32 timing, uhs_max_dtr;
    UINT32 ctrl;
    CPUartLogPrintf("sdio_init_uhs_card enter");
    //set the driver strength for the card
    sdio_select_driver_type();

    //set bus speed mode of the card
    err = sdio_set_bus_speed_mode(&timing, &uhs_max_dtr);
    if(err)
    {
        CPUartLogPrintf("sdio_init_uhs_card error: err of sdio_set_bus_speed_mode is %d", err);
        return err;
    }
    sdio_timing = timing;
	ctrl = sdio_readl(SD_CTRL_BASE_ADDR,SDHCI_MMC_CTRL);
	ctrl |= (1<<1);
	sdio_writel(SD_CTRL_BASE_ADDR,SDHCI_MMC_CTRL,ctrl);
	/*
	 * SPI mode doesn't define CMD19 and tuning is only valid for SDR50 and
	 * SDR104 mode SD-cards. Note that tuning is mandatory for SDR104.
	 */
	if (timing == MMC_TIMING_UHS_SDR50 ||
	    timing == MMC_TIMING_UHS_SDR104)
    {
        CPUartLogPrintf("sdio_init_uhs_card: will call asr_execute_tuning ios: %d hz\n", uhs_max_dtr);
    	err = asr_execute_tuning(timing);
    	CPUartLogPrintf("sdio_init_uhs_card: after call asr_execute_tuning ios: %d hz\n", uhs_max_dtr);
    }

    return err;
}

static void sdio_postpone_clk_gate()
{
	UINT32 base_addr = SD_CTRL_BASE_ADDR;
	UINT32 ctrl = sdio_readl(base_addr, SDHCI_OP_EXT_CFG);
	ctrl = (ctrl&~(0xf<<16))|(0xf<<16);
	sdio_writel(base_addr,SDHCI_OP_EXT_CFG,ctrl);
	CPUartLogPrintf("sdio_fpga_postpone_clk_gate: SDHCI_OP_EXT_CFG(0x%x)=0x%x", SDHCI_OP_EXT_CFG, sdio_readl(base_addr,SDHCI_OP_EXT_CFG));
#if 0
    ctrl = sdio_readl(base_addr, SDHCI_ACMD12_ERR);
	ctrl = ctrl&~(1<<30);
	sdio_writel(base_addr,SDHCI_ACMD12_ERR,ctrl);
	CPUartLogPrintf("sdio_fpga_postpone_clk_gate: SDHCI_ACMD12_ERR(0x%x)=0x%x", SDHCI_ACMD12_ERR, sdio_readl(base_addr,SDHCI_ACMD12_ERR));
#endif
}

UINT8 sdio_get_current_timing(void)
{
    return sdio_timing;
}

static void set_ddr_sdh_axi_qos()
{
	UINT32 ctrl = sdio_readl(0xd4282c00, 0x118);
	ctrl |= (4<<16);
	sdio_writel(0xd4282c00,0x118,ctrl);
	CPUartLogPrintf("set_ddr_sdh_axi_qos 0x%x",sdio_readl(0xd4282c00, 0x118));
}

static void sdio_set_clock(int forceon)
{
	UINT32 value = sdio_readl(SD_CTRL_BASE_ADDR, 0x108);
	if (forceon)
		value |= (0x3<<11); //force sdio clock always on
	else
		value &= ~(0x3<<11);
	sdio_writel(SD_CTRL_BASE_ADDR, 0x108, value);
}

int sdio_f0_readb(unsigned long reg, unsigned char *data)
{
    return SDIO_CMD52(0, 0, (unsigned)reg, NULL, (UINT8*)data);
}

/**
 *  @brief This function writes data into card register.
 *  This function use function 1, because function 1 represent wifi
 *
 *  @reg:          register offset
 *  @data:         value
 *
 *  @return 0 if succeed, otherwise Err number
*/
STATUS NU_SDIODrv_Write_Register(UNSIGNED reg, UNSIGNED data)
{
    return SDIO_CMD52(1, sdiofnnum, (unsigned)reg, (UINT8)data, NULL);
}

/**
 *  This function reads data from card register.
 *  This function use function 1, because function 1 represent wifi
 *
 *  @reg:          register offset
 *  @data:         value
 *
 *  @return 0 if succeed, otherwise Err number
*/
STATUS NU_SDIODrv_Read_Register(UNSIGNED reg, UNSIGNED *data)
{
    return SDIO_CMD52(0, sdiofnnum, (unsigned)reg, NULL, (UINT8*)data);
}

/**
 *  This function write a command/data packet to card.
 *  This function use function 1, because function 1 represent wifi
 *
 *  @port: 	    Port number for sent
 *  @buffer:   Pointer to buffer data
 *  @buf_len:	    Buffer length
 *
 *  @return 0 if succeed, otherwise Err number
*/
STATUS NU_SDIODrv_Write_Memory(UNSIGNED_INT ioport, BYTE_PTR buffer, UNSIGNED buf_len)
{
	int ret = 0;
	sdio_set_wakelock();

#ifdef QUECTEL_PROJECT_CUST
#if defined(SDIO_CLOCK_ADJ)
	sdio_set_clock(0);
#endif
#else
#if defined(SDIO_CLOCK_ADJ) && defined(SSVWIFI_FUNCTION)
	sdio_set_clock(0);
#endif
#endif

#ifndef ENABLE_SDIO_ADMA
    CacheCleanMemory(buffer, buf_len);

#ifdef QUECTEL_PROJECT_CUST
#ifdef AICWIFI_SUPPORT
	ret = sdio_io_rw_ext_helper(sdiofnnum, 1, (unsigned)ioport, 0, buffer, buf_len);
#else
    ret = sdio_io_rw_ext_helper(sdiofnnum, 1, (unsigned)ioport, 1, buffer, buf_len);
#endif
#else
	ret = sdio_io_rw_ext_helper(sdiofnnum, 1, (unsigned)ioport, 1, buffer, buf_len);
#endif
#else
	sdio_sg_list pbuf;
	pbuf.buf = (unsigned char * )buffer;
	pbuf.len = buf_len;
	ret = sdio_adma_sg(sdiofnnum, 1, (unsigned)ioport, 0, (unsigned char *)&pbuf, 1);
#endif

#ifdef QUECTEL_PROJECT_CUST
#if defined(SDIO_CLOCK_ADJ)
	sdio_set_clock(1);
#endif
#else
#if defined(SDIO_CLOCK_ADJ) && defined(SSVWIFI_FUNCTION)
	sdio_set_clock(1);
#endif
#endif

	sdio_clear_wakelock();
	return ret;
}

/**
 *  This function read data packet/event/command from card.
 *  This function use function 1, because function 1 represent wifi
 *
 *  @port: 	    Port number for sent
 *  @buffer:       Pointer to buffer data
 *  @buf_len:	    Buffer length
 *
 *  @return 0 if succeed, otherwise Err number
*/
STATUS NU_SDIODrv_Read_Memory(UNSIGNED_INT ioport, BYTE_PTR buffer, UNSIGNED buf_len)
{
	int ret = 0;
	unsigned int nents = (buf_len>ADMA_MAX_SEG_SIZE)?((buf_len+ADMA_MAX_SEG_SIZE-1)/ADMA_MAX_SEG_SIZE):1;
	sdio_sg_list sg[4];

	sdio_set_wakelock();

#ifdef QUECTEL_PROJECT_CUST
#if defined(SDIO_CLOCK_ADJ)
	sdio_set_clock(0);
#endif
#endif

#ifndef ENABLE_SDIO_ADMA
    CacheInvalidateMemory(buffer, buf_len);
#ifdef QUECTEL_PROJECT_CUST
#ifdef AICWIFI_SUPPORT
    ret =  sdio_io_rw_ext_helper(sdiofnnum, 0, (unsigned)ioport, 0, buffer, buf_len);
#else
	ret =  sdio_io_rw_ext_helper(sdiofnnum, 0, (unsigned)ioport, 1, buffer, buf_len);
#endif
#else
	ret =  sdio_io_rw_ext_helper(sdiofnnum, 0, (unsigned)ioport, 1, buffer, buf_len);
#endif
#else
	if (nents > 1) {
        int i = 0;
	    unsigned int left_size = buf_len, seg_size = ADMA_MAX_SEG_SIZE;
        BYTE_PTR ptr = buffer;
        ASSERT(nents<=sizeof(sg));
        for(;i<nents;i++)
        {
            seg_size = (left_size>seg_size)?seg_size:left_size;
            sg[i].buf = ptr;
            sg[i].len = seg_size;
            ptr = (unsigned char * )((unsigned int)ptr+seg_size);
            left_size -= seg_size;
        }
	} else {
    	sg[0].buf = (unsigned char * )buffer;
    	sg[0].len = buf_len;
	}
    ret = sdio_adma_sg(sdiofnnum, 0, (unsigned)ioport, 0, (unsigned char *)sg, nents);
#endif

#ifdef QUECTEL_PROJECT_CUST
#if defined(SDIO_CLOCK_ADJ)
	sdio_set_clock(1);
#endif
#endif

	sdio_clear_wakelock();
	return ret;
}

#ifdef ENABLE_SDIO_ADMA
STATUS NU_SDIODrv_Write_Memory_sg(UNSIGNED_INT ioport, BYTE_PTR buffer, UNSIGNED buf_len)
{
	int ret = 0;

	sdio_set_wakelock();

#ifdef QUECTEL_PROJECT_CUST
#if defined(SDIO_CLOCK_ADJ)
	sdio_set_clock(0);
#endif
#else
#if defined(SDIO_CLOCK_ADJ) && defined(SSVWIFI_FUNCTION)
	sdio_set_clock(0);
#endif
#endif

#ifdef QUECTEL_PROJECT_CUST
#ifdef	SDIO_DATA_POLL
	amda_seg_cnt = buf_len;
#endif
#endif

	ret = sdio_adma_sg(sdiofnnum, 1, (unsigned)ioport, 0, (unsigned char *)buffer, buf_len);

#ifdef QUECTEL_PROJECT_CUST
#if defined(SDIO_CLOCK_ADJ)
	sdio_set_clock(1);
#endif
#else
#if defined(SDIO_CLOCK_ADJ) && defined(SSVWIFI_FUNCTION)
	sdio_set_clock(1);
#endif
#endif

	sdio_clear_wakelock();
	return ret;
}

STATUS NU_SDIODrv_Read_Memory_sg(UNSIGNED_INT ioport, BYTE_PTR buffer, UNSIGNED buf_len)
{
	int ret = 0;

	sdio_set_wakelock();

#ifdef QUECTEL_PROJECT_CUST
#if defined(SDIO_CLOCK_ADJ)
	sdio_set_clock(0);
#endif
#endif

    ret = sdio_adma_sg(sdiofnnum, 0, (unsigned)ioport, 0, (unsigned char *)buffer, buf_len);

#ifdef QUECTEL_PROJECT_CUST
#if defined(SDIO_CLOCK_ADJ)
	sdio_set_clock(1);
#endif
#endif

	sdio_clear_wakelock();
	return ret;
}

extern void issue_sync_barrier(void);

sdio_desc_table * sdio_create_admatable(UINT8 *buf, unsigned listcnt, int write, UINT32 *size)
{
    int i = 0;
    int desc_num = listcnt;
    sdio_desc_table * phead = NULL;
    sdio_sg_list *pbuf = (sdio_sg_list *)buf;
    UINT32  sum = 0;
    unsigned short attr = 0;

#ifdef QUECTEL_PROJECT_CUST
    #ifdef CONFIG_ADMA_PROTECT
    UINT32 cpsr = 0;
    #endif /* CONFIG_ADMA_PROTECT */
#endif

    //CPUartLogPrintf("sdio_create_desctable enter");

    //CPUartLogPrintf("pbuf:%08x, listcnt:%d, write:%d", pbuf, listcnt, write);

    sdio_desc_table * desc_t = sdioadmatl;
    #if 0
    if(desc_t == NULL || desc_num > SDIO_ADMATL_NUM)
    {
        CPUartLogPrintf("desc talbe is %d", desc_num);
        return NULL;
    }
    #endif
    //memset(desc_t, 0, (sizeof(sdio_desc_table)*(SDIO_ADMATL_NUM)));

    phead = desc_t;

    //CPUartMsgPrintf("sdio adma table:");
    for(i = 0; i<desc_num; ++i)
    {
        phead->addr = (unsigned int)pbuf->buf;
        phead->length = pbuf->len;
        if(i == desc_num-1)
            attr = ATTR_VAL | ATTR_ACT2 | ATTR_END;
        else
            attr = ATTR_VAL | ATTR_ACT2;
        phead->attr = attr;

        sum += pbuf->len;

        //CPUartLogPrintf("sdio_create_admatable: no%d phead:0x%08x addr:0x%08x leng:%d attr:0x%08x sum:%d",
        //    i, phead, phead->addr, phead->length, phead->attr, sum);
        if(write)
        {
	#ifdef QUECTEL_PROJECT_CUST
        #ifdef CONFIG_ADMA_PROTECT
            cpsr = disableInterrupts();
            CacheCleanMemory(pbuf->buf, pbuf->len);
            restoreInterrupts(cpsr);
        #else
            CacheCleanMemory(pbuf->buf, pbuf->len);
            {
                static UINT32 index1 = 0,index2 = 0;
                for(index1 = 0; index1 < 1; index1++)
                    index2++;
                index2 = (UINT32)sdioadmatl + pbuf->buf[0] + pbuf->buf[pbuf->len-1];

            }
            issue_sync_barrier();

            CacheCleanMemory(pbuf->buf, pbuf->len);
            issue_sync_barrier();
        #endif /* CONFIG_ADMA_PROTECT */
	#else
		CacheCleanMemory(pbuf->buf, pbuf->len);
	#endif
        }
        else
        {
            CacheInvalidateMemory(pbuf->buf, pbuf->len);
        }
	#ifdef QUECTEL_PROJECT_CUST
        issue_sync_barrier();
	#endif
        phead++;
        pbuf++;
    }

    *size = sum;

    /*
    CPUartLogPrintf("description table");
    phead = desc_t;
    for(i = 0; i<desc_num+1; i++)
    {
        CPUartLogPrintf("==addr:%08x, leng:%d, attr:%08x==value:%08x,%08x", phead->addr, phead->length, phead->attr,
            *(UINT32*)phead, *((UINT32*)phead+1));
        phead++;
    }
    */

    //CPUartLogPrintf("sdio_create_desctable leave");
    return desc_t;
}


static int sdio_adma_sg(unsigned fn, int write, unsigned addr, int incr_addr, UINT8 *buf, unsigned listcnt)
{
    UINT16    cmd = 0;
    UINT32    arg = 0;
    UINT16   mode = 0;
    UINT32 blocks = 0;
    UINT32  blksz = 0;
    UINT32   size = 0;
    int ret;

    OSASemaphoreAcquire(SDIOSysRef, OS_SUSPEND);

    sdio_desc_table *pdesc = sdio_create_admatable(buf, listcnt, write, &size);

    //CPUartMsgPrintf("size = %d", size);
    //if(pdesc == NULL)
    //    return -1;

    cmd = 0x353a; //cmd53 3522
    arg = write ? 0x80000000 : 0x00000000;
    #if 0
    arg |= fn << 28;
    arg |= 0; //fix address
    arg |= addr << 9;
    #endif
    arg |= (fn << 28)|(addr << 9)|(incr_addr << 26);

    if(size < FUNCTION_BLOCKSIZE)
    {
        blocks = 1;
        blksz = size;
    }
    else
    {
        blocks = size/FUNCTION_BLOCKSIZE;
        blksz = FUNCTION_BLOCKSIZE;
    }


    if (blocks == 1 && blksz < FUNCTION_BLOCKSIZE)
        arg |= blksz;   /* byte mode */
    else
        arg |= 0x08000000 | blocks;     /* block mode */

    //transfer mode
    mode = SDHCI_TRNS_BLK_CNT_EN | SDHCI_TRNS_DMA;
    if (blocks>1)
        mode |= SDHCI_TRNS_MULTI;
    if (write == 0)
        mode |= SDHCI_TRNS_READ;

    //printdesctbl(sdioadmatl);
    //CPUartMsgPrintf("block:%d,blksz:%d", blocks, blksz);
    ret = sdio_adma_data(cmd, arg, pdesc, blocks, blksz, mode);
    OSASemaphoreRelease(SDIOSysRef);

    return ret;
}


static int sdio_adma_data(UINT16 cmd, UINT32 arg, sdio_desc_table* buf, unsigned blocks, unsigned blksz, UINT16 mode)
{
    volatile UINT32* ptr32;
    volatile UINT16* ptr16;
    unsigned long long timeout = 0;
    UINT32  mask;
    UINT8   ctrl;
    OS_STATUS status;
    int sem_num;
    UINT32 try_num = 20; // 1 senconds

    CMD_COMP_status = 0;
    DATA_COMP_status = 0;

    //set IRQ
    //sdio_card_int(1);
    //CPUartMsgPrintf("cmd:%08x,arg:%08x,buf:%08x,blocks:%d,blksize:%d,mode:%08x", cmd, arg, buf, blocks, blksz, mode);

    timeout = 1000000;
    mask = SDHCI_CMD_INHIBIT | SDHCI_DATA_INHIBIT;
    do
    {
        if(!(sdio_readl(SD_CTRL_BASE_ADDR, SD_PRESENT_STAT_0_offset)&mask))
            break;
    }while(--timeout);

    if(timeout == 0)
    {
        //sdhci_dumpregs(SD_CTRL_BASE_ADDR);
        CPUartLogPrintf("SDIOERR:53Controller %s never released inhabit bit %x\r\n", (mode&SDHCI_TRNS_READ)? "Read": "Write",
            (sdio_readl(SD_CTRL_BASE_ADDR, SD_PRESENT_STAT_0_offset)));
        sdio_sw_reset(SD_CTRL_BASE_ADDR);
        return MMC_ERR_CMD_TOUT;
    }

    //set timeout
    sdio_writeb(SD_CTRL_BASE_ADDR, SD_SW_RESET_CTRL_offset, 0x0E);

    //set host ctrl
	ctrl = sdio_readb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset);
    ctrl = (ctrl & ~SDHCI_CTRL_DMA_MASK)| SDHCI_CTRL_ADMA32;
#if 0
    CPUartLogPrintf("cmd_data host ctrl = 0x%02x\r\n", ctrl);
#endif
	sdio_writeb(SD_CTRL_BASE_ADDR, SD_HOST_CTRL_offset, ctrl);

    //set dma addr
    sdio_writel(SD_CTRL_BASE_ADDR, SD_ADMA_SYS_ADDR_offset, (UINT32)buf);

    //set block size and count
    sdio_writew(SD_CTRL_BASE_ADDR, SD_BLOCK_SIZE_offset, blksz);
	sdio_writew(SD_CTRL_BASE_ADDR, SD_BLOCK_COUNT_offset, blocks);

    //set arg
    ptr32 = (UINT32*) (SD_CTRL_BASE_ADDR + SD_ARG_LOW_offset);
	*ptr32 = arg;
    //CPUartLogPrintf("cmd_data arg = 0x%x\r\n", arg);

    //set transfer mode
    //CPUartLogPrintf("cmd_data transfer mode = 0x%x\r\n", mode);
    sdio_writew(SD_CTRL_BASE_ADDR, SD_TRANSFER_MODE_offset, mode);

	//set cmd
    ptr16 = (UINT16*) (SD_CTRL_BASE_ADDR + SD_CMD_offset);
    *ptr16 = cmd;

#ifdef QUECTEL_PROJECT_CUST
    timeout = 100000;   //0x8fffffff;
#else
	timeout = 1000000;
#endif
	do
    {
	    if(CMD_COMP_status)
            break;
	}while (--timeout);

    if (timeout == 0)
    {
        CPUartLogPrintf("sdio_adma_data SDIOERR:Wait for command time out!\r\n");
        sdio_sw_reset(SD_CTRL_BASE_ADDR);
        return MMC_ERR_CMD_TOUT;
    }


TRY_LL:
#ifdef QUECTEL_PROJECT_CUST
#ifdef SDIO_DATA_POLL
    {
    	status = OS_SUCCESS;
		unsigned long count = 0;
		unsigned long tick = SDIO_DATA_TICK*1000*2000;

		if(amda_seg_cnt >= CONFIG_POLL_TH)
		{
			while(1)
			{
				OSASemaphorePoll(sdio_sem_data, &count);
				if(count)
				{
					OSASemaphoreAcquire(sdio_sem_data, SDIO_DATA_TICK);
					break;
				}

				tick--;
				sdio_wait(100);
				if(tick == 0)
				{
	                status = OS_TIMEOUT;
	                break;
				}
			}
		}
		else
		{
			status = OSASemaphoreAcquire(sdio_sem_data, SDIO_DATA_TICK);
		}
    }
#else
    status = OSASemaphoreAcquire(sdio_sem_data, SDIO_DATA_TICK);
#endif /* SDIO_DATA_POLL */
#else
	status = OSASemaphoreAcquire(sdio_sem_data, SDIO_DATA_TICK);
#endif
    if (status == OS_TIMEOUT)
    {
        if(intstsrec & XFER_COMP)
        {
        }
        else if(intstsrec & ADMA_ERR)
        {
            CPUartLogPrintf("sdio_adma_data: ADMA_ERR");
        }
        else if(--try_num)
        {
            #if 1
            CPUartLogPrintf("sdio_adma_data: data timeout blocks=%d blksz=%d try_num=%d", blocks, blksz, try_num);
            timeout = 1000000;
        	while (--timeout)
        	{
        		if(intstsrec & XFER_COMP)
        		{
                    return 0;
                }
        	}
            #endif
            CPUartLogPrintf("sdio_adma_data sema timeout,try wait");
            goto TRY_LL;
        }
        else
        {

            ptr16 = (UINT16*)(SD_CTRL_BASE_ADDR + SD_RESP_0_offset);
            MMC_RESPONSE[0] = *ptr16;

            CPUartLogPrintf("cmd53 resp0 = 0x%x\r\n", MMC_RESPONSE[0]);

            if (MMC_RESPONSE[0] & (R5_ERROR | R5_FUNCTION_NUMBER | R5_OUT_OF_RANGE))
            {
                CPUartLogPrintf("sdio_adma_data SDIOERR:MMC_ERR_IO");
                return MMC_ERR_IO;
            }

            printdesctbl(buf);
            sdhci_dumpregs(SD_CTRL_BASE_ADDR);

            RTI_LOG("sdio_adma_data SDIOERR:INT status:%x, bufaddr:%x, blk:%d, R5:%x", intstsrec, buf, blocks, MMC_RESPONSE[0]);

			RTI_LOG("sdio_adma_data SDIOERR:Wait for %s data transfer time out!\r\n",
			                    (mode&SDHCI_TRNS_READ)? "Read": "Write");
            sdio_sw_reset(SD_CTRL_BASE_ADDR);
			return MMC_ERR_DATA_TOUT;
        }
    }

    if(cmderror == 1)
    {
    	RTI_LOG("sdio_adma_data: cmderror INT status:0x%lx ERR_INT status:0x%x, blk:%d left blks:%d", intstsrec, errintstsrec, blocks, sdio_readw(SD_CTRL_BASE_ADDR, SD_BLOCK_COUNT_offset));
        sdhci_dumpregs(SD_CTRL_BASE_ADDR);
        sdio_sw_reset(SD_CTRL_BASE_ADDR);
        return MMC_ERR_DATA_CRC;
    }

    return 0;

}

void printdesctbl(sdio_desc_table *head)
{
    sdio_desc_table *phead = head;
    CPUartLogPrintf("description table");
    while(phead != NULL && phead->length != 0)
    {
        CPUartLogPrintf("addr:%08x, leng:%d, attr:%08x", phead->addr, phead->length, phead->attr);
        if(phead->attr == 0x23)
            break;
        phead++;
    }
}
#endif

int sdio_reset(void)
{
    CPUartLogPrintf("sdio reset");
	int ret = sdio_host_init();
	if (!ret)
	{
    	CPUartLogPrintf("sdio reinit OK");
	}
	else
	{
		CPUartLogPrintf("SDIOERR:sdio reinit error");
	}
    return ret;
}

void sdio_reset_wifi(void)
{
    volatile unsigned long *r;
#ifndef SSVWIFI_FUNCTION
    int rst_gpio = platform_5803_get_hw_reset_gpio();
#else
#ifdef QUECTEL_PROJECT_CUST
	int rst_gpio = GPIO_PIN_10;
#else
	int rst_gpio = GPIO_PIN_55;
#endif
#endif
    CPUartLogPrintf("sdio_reset_wifi: rst_gpio=%d", rst_gpio);
    if(rst_gpio == -1)
        return;
	//pdn
	GpioSetLevel(rst_gpio, GPIORC_LOW);

	//wait for 100ms
	if(GetWiFiType() == 6)
        sdio_mswait(1000);
    else
        sdio_mswait(10);

    GpioSetLevel(rst_gpio, GPIORC_HIGH);
	
#ifdef QUECTEL_PROJECT_CUST
#ifdef AICWIFI_SUPPORT
    {
        extern int wlan_ldo_en_pin;

        if(wlan_ldo_en_pin == -1)
            return;
        GpioSetLevel(wlan_ldo_en_pin, GPIORC_HIGH);
    }
#endif
#endif
}

#ifndef SSVWIFI_FUNCTION
int get_host_wake_wlan_gpio_no(void)
{
	return HOST_WAKEUP_WIFI;
}

int get_sdio2host_gpio_no(void)
{
	return WIFI_INT_PIN;
}

int get_ldo_en_gpio_no(void)
{
	return WIFI_PWREN_PIN;
}

int platform_5803_get_hw_reset_gpio(void)
{
    return WIFI_RESET_PIN;
}
#endif

static void sdio_find_consecutiveones(UINT8 arr[], int size, int *lmin, int *lmax)
{
    int start = -1, end = -1;
    int min = 0, max = 0;
	int i = 0;
    for (i = 0; i < size; i++)
    {
        if (arr[i] == 1 && start == -1)
        {
            start = i;
            end = -1;
        }
        else if (arr[i]!= 1 && start!= -1)
        {
            end = i - 1;
            if (end-start>max-min)
            {
                min = start;
                max = end;
            }
            start = -1;
        }
    }

    if (start!= -1 && end == -1)
	{
        if (size - 1- start> max-min)
        {
            min = start;
            max = size - 1;
        }
    }

    CPUartLogPrintf("min:%d,max:%d\n",min,max);
	*lmax = max;
	*lmin = min;
}


static UINT32 sdio_cmd19_dma(UINT16 cmd, UINT32 argv, UINT16 blk_size, UINT16 blk_cnt, UINT32 buffer)
{
	UINT32 timeout,mask;
	OSA_STATUS status;
	volatile UINT32* ptr32;
	UINT32 base = SD_CTRL_BASE_ADDR;

    CMD_COMP_status = 0;
    DATA_COMP_status = 0;

	timeout = 10000;
    mask = SDHCI_CMD_INHIBIT;
    while((sdio_readl(base, SD_PRESENT_STAT_0_offset))&mask)
    {
        if(timeout == 0)
        {
            CPUartLogPrintf("%s,Controller never released inhabit bit", __FUNCTION__);
            return MMC_ERR_CMD_TOUT;
        }
        timeout--;
    }

	CacheInvalidateMemory((void *)buffer, 0x40);
	sdio_writel(base, SD_SYSADDR_LOW_offset, buffer);

	/*set block size and block count*/
	ptr32 = (UINT32*) (base + SD_BLOCK_SIZE_offset);
	*ptr32 = blk_size | (blk_cnt << 16);

	ptr32 = (UINT32*) (base + SD_ARG_LOW_offset);
	*ptr32 = argv;

	//dma read
	sdio_writew(base, SD_TRANSFER_MODE_offset, 0x11);
	sdio_writew(base, SD_CMD_offset, cmd);

	timeout = 100000;
	while (--timeout)
	{
		if(CMD_COMP_status)
			break;
	}

	if (timeout == 0)
	{
		CPUartLogPrintf("SDIOERR:Wait for cmd19 time out!\r\n");
		sdio_sw_reset(SD_CTRL_BASE_ADDR);
		return MMC_ERR_CMD_TOUT;
	}

	status = OSASemaphoreAcquire(sdio_sem_data, 5);
	if (status != OS_SUCCESS)
	{
		CPUartLogPrintf("SDIOERR:Wait for data time out!\r\n");
	}

	return 0;
}

/**
 *  @brief: This function tunes the RX windows with the given clock.
 *  @return the delay value
 */
static int sdio_rx_tune(UINT32 base_addr)
{
	UINT16 count = 0;
	UINT8 data_block[64];
	UINT8 match[256] = {0};

	memset(data_block, 0, 64);

	UINT16 transmode = sdio_readw(base_addr, SD_TRANSFER_MODE_offset);
	sdio_writew(base_addr, SD_TRANSFER_MODE_offset, transmode|0x1); //PIO mode

	//TX delayline
	UINT32 dctrl = sdio_readl(base_addr, 0x130);
	dctrl &= ~(0xFF<<24);
	dctrl |= (0x32<<24);
	sdio_writel(base_addr, 0x130, dctrl);

	UINT32 dcfg = sdio_readl(base_addr, 0x134);
	dcfg &= ~(0xFF<<16);
	dcfg |= (0x4<<16);
	sdio_writel(base_addr, 0x134, dcfg);

	UINT32 val = sdio_readl(base_addr, 0x11C);
	sdio_writel(base_addr, 0x11C, val|(0x80000000));

	//enable delayline
	dcfg = sdio_readl(base_addr, 0x134);
	dcfg &= ~(0xFF);
	dcfg |= 0x4;
	sdio_writel(base_addr, 0x134, dcfg);

	UINT32 rxcfg = sdio_readl(base_addr, 0x118);
	rxcfg &= ~(0x3<<2);
	rxcfg |= (0x1<<2);
	sdio_writel(base_addr, 0x118, rxcfg);

	//step=1
	rxcfg = sdio_readl(base_addr, 0x118);
	rxcfg &= ~(0x3FF<<18);
	rxcfg |= (0x1<<18);
	sdio_writel(base_addr, 0x118, rxcfg);

	dctrl = sdio_readl(base_addr, 0x130);
	dctrl |= (0x1);
	dctrl &= ~(0xFF<<16);
	sdio_writel(base_addr, 0x130, dctrl);

	memset(match, 0, sizeof(match));
	count = 0;
	while(count < 256)
	{
		memset(data_block, 0, sizeof(data_block));

		//send cmd19
		UINT32 ret = sdio_cmd19_dma(0x133a, 0, 64, 1, ( UINT32)data_block);
#if 0
		{
			int i = 0;
			RTI_LOG("get data:\n");
			for(i=0; i<64; i+=4)
			{
				RTI_LOG("%02x%02x%02x%02x\n",data_block[i],data_block[i+1],data_block[i+2],data_block[i+3]);
			}
		}
#endif
		if(ret == 0 && (memcmp(data_block, tuning_pattern, 64) == 0))
			match[count] = 1;

		count++;

		dctrl = sdio_readl(base_addr, 0x130);
		dctrl &= ~(0xFF<<16);
		dctrl |= (count<<16);
		dctrl |= (0x1);
		sdio_writel(base_addr, 0x130, dctrl);
	}

#if 0
	RTI_LOG("match point:\n");
	for (count = 0; count < 256; count++)
	{
		if (match[count])
		{
			RTI_LOG("%d\n",count);
		}
	}
#endif

	int min = 0,max = 0;
	sdio_find_consecutiveones(match, sizeof(match)/sizeof(match[0]), &min, &max);
	dctrl = sdio_readl(base_addr, 0x130);
	dctrl &= ~(0xFF<<16);
	dctrl |= (((min+max)/2)<<16);
	sdio_writel(base_addr, 0x130, dctrl);

	if (min == 0 && max == 0)
		return -1;

	return 0;
}

static void sdio_rx_cfg(UINT32 base_addr, int delaycode)
{
	UINT32 rxcfg = sdio_readl(base_addr, 0x118);
	rxcfg &= ~(0x3<<2);
	rxcfg |= (0x1<<2);
	sdio_writel(base_addr, 0x118, rxcfg);

	UINT32 dctrl = sdio_readl(base_addr, 0x130);
	dctrl &= ~(0xFF<<16);
	dctrl |= (delaycode<<16);
	dctrl |= (0x1);
	sdio_writel(base_addr, 0x130, dctrl);
}

#ifdef QUECTEL_PROJECT_CUST

#define SDIO_PHASE_FILE     "SDIO_PHASE.bin"
#define SDIO_PHASE_SIZE     sizeof(int)
#define SDIO_PHASE_BUFF     10

extern int FDI_Access(const char *path, UINT32 mode);
extern char *itoa(int value,char *string,int radix);
/**
 * add by breeze 20250313
 * add functions for set and get sdio phase
 */

/**
 * @brief: read sdio phase from file
 * @return: 0~255 : sdio RX_DLINE_CODE
 *          -1    : error
 */
int sdio_read_phase_from_fs(void)
{
    int fd = 0;
    int size = 0;
    int sdio_phase = 0;
    char *buffer[SDIO_PHASE_BUFF] = {0};
    CPUartLogPrintf("enter %s", __FUNCTION__);

    fd = FDI_fopen(SDIO_PHASE_FILE,"rb");
    if(fd == 0)
    {
        CPUartLogPrintf("%s, open %s failed!\r\n", __FUNCTION__, SDIO_PHASE_FILE);
        return -1;
    }

    size = FDI_fread(buffer, 1, SDIO_PHASE_SIZE, fd);
    if(size != SDIO_PHASE_SIZE)
    {
        CPUartLogPrintf("%s, read sdio_phase error!!!, SDIO_PHASE_SIZE=%d, read size=%d\r\n", __FUNCTION__, SDIO_PHASE_FILE, size);
        FDI_fclose(fd);
        return -1;
    }
    
    sdio_phase = atoi((const char *)buffer);
    FDI_fclose(fd);
    CPUartLogPrintf("%s, sdio_phase=%d\r\n", __FUNCTION__, sdio_phase); 

    return sdio_phase;
}

/**
 * @brief: write sdio phase to file
 * @return: 0   : success
 *          -1  : error
 *          1   : not support SDIO_PHASE
 */
int sdio_write_phase_to_fs(const int new_phase)
{
    CPUartLogPrintf("enter %s", __FUNCTION__);
    if(new_phase < 0 || new_phase > 255)
        return -1;

    int fd = 0;
    int size = 0;
    int sdio_phase = 0;
    char *buffer[SDIO_PHASE_BUFF] = {0};

#if SDIO_PHASE
    CPUartLogPrintf("[%s]host sdio new phase:%d\n", __FUNCTION__, new_phase);

    fd = FDI_fopen(SDIO_PHASE_FILE, "wb");
    if(fd == 0)
    {
        CPUartLogPrintf("%s, open %s failed!\r\n", __FUNCTION__, SDIO_PHASE_FILE);
        return -1;
    }

    itoa(new_phase, (char *)buffer, SDIO_PHASE_BUFF);

    size = FDI_fwrite(buffer, 1, SDIO_PHASE_SIZE, fd);
    if(size != SDIO_PHASE_SIZE)
    {
        CPUartLogPrintf("%s, write sdio_phase error!!!, SDIO_PHASE_SIZE=%d, write size=%d\r\n", __FUNCTION__, SDIO_PHASE_FILE, size);
        FDI_fclose(fd);
        return -1;
    }

    FDI_fclose(fd);

    return 0;
#else
    return 1; // not support SDIO_PHASE
#endif /* SDIO_PHASE */
}

/**
 * @brief: remove sdio phase file
 * @return: 0   : success
 *          -1  : error
 */
int sdio_remove_phase_from_fs(void)
{
    CPUartLogPrintf("enter %s", __FUNCTION__);

    int ret  = FDI_Access(SDIO_PHASE_FILE, 0);
    if (0 == ret)
    {
        ret = FDI_remove(SDIO_PHASE_FILE);
        if(0 != ret)
        {
            CPUartLogPrintf("%s: Warning, FDI_remove failed, ret %d", __FUNCTION__, ret);
            return -1;
        }
    }
    return 0;
}

#endif /* QUECTEL_PROJECT_CUST */

static int sdio_set_sdr50()
{
    UINT32 bus_speed, timing;
    int err = -1;
    UINT8 speed;
    UINT32  uhs_max_dtr;

	bus_speed = SDIO_SPEED_SDR50;
	timing = MMC_TIMING_UHS_SDR50;
	uhs_max_dtr = UHS_SDR50_MAX_DTR;


	err = SDIO_CMD52(0, 0, 0x14, 0, &speed);
	if (err)
		return err;

	CPUartLogPrintf("checking UHS-I support:0x%x", speed);

	if (speed & 0x1)
	{
		CPUartLogPrintf("card support SDR50");
	}
	else
	{
		CPUartLogPrintf("card doesn't support SDR50");
		return -1;
	}

	err = SDIO_CMD52(0, 0, SDIO_CCCR_SPEED, 0, &speed);
	if (err)
		return err;

	speed &= ~SDIO_SPEED_BSS_MASK;
	speed |= bus_speed;
	err = SDIO_CMD52(1, 0, SDIO_CCCR_SPEED, speed, 0);
	if (err)
		return err;

    UINT16 clk = sdio_readw(SD_CTRL_BASE_ADDR, SD_CLOCK_CTRL_offset);
    clk &= ~SDHCI_CLOCK_CARD_EN;
    sdio_writew(SD_CTRL_BASE_ADDR, SD_CLOCK_CTRL_offset, clk);


    UINT16 ctrl = 0;
    //uhs mode
    ctrl = sdio_readw(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2);
    CPUartLogPrintf("%s,SDHCI_HOST_CTRL2:0x%x", __FUNCTION__, ctrl);
    ctrl |= 0xA;
    sdio_writew(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2, ctrl);

	return 0;
}

static int sdio_set_sdr104()
{
    UINT32 bus_speed, timing;
    int err = -1;
    UINT8 speed;
    UINT32  uhs_max_dtr;

	bus_speed = SDIO_SPEED_SDR104;
	//timing = MMC_TIMING_UHS_SDR50;
	//uhs_max_dtr = UHS_SDR50_MAX_DTR;


	err = SDIO_CMD52(0, 0, 0x14, 0, &speed);
	if (err)
		return err;

	CPUartLogPrintf("checking UHS-I support:0x%x", speed);

	if (speed & 0x1)
	{
		CPUartLogPrintf("card support SDR50");
	}
	else
	{
		CPUartLogPrintf("card doesn't support SDR50");
		return -1;
	}

	err = SDIO_CMD52(0, 0, SDIO_CCCR_SPEED, 0, &speed);
	if (err)
		return err;

	speed &= ~SDIO_SPEED_BSS_MASK;
	speed |= bus_speed;
	err = SDIO_CMD52(1, 0, SDIO_CCCR_SPEED, speed, 0);
	if (err)
		return err;

    UINT16 clk = sdio_readw(SD_CTRL_BASE_ADDR, SD_CLOCK_CTRL_offset);
    clk &= ~SDHCI_CLOCK_CARD_EN;
    sdio_writew(SD_CTRL_BASE_ADDR, SD_CLOCK_CTRL_offset, clk);


    UINT16 ctrl = 0;
    //uhs mode,SDR104
    ctrl = sdio_readw(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2);
    CPUartLogPrintf("%s,SDHCI_HOST_CTRL2:0x%x", __FUNCTION__, ctrl);
    ctrl |= 0xB;
    sdio_writew(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2, ctrl);

	return 0;
}


static int sdio_set_sdr25()
{
    UINT32 bus_speed;
    int err;
    UINT8 speed;

	bus_speed = SDIO_SPEED_SDR25;

	err = SDIO_CMD52(0, 0, SDIO_CCCR_SPEED, 0, &speed);
	if (err)
		return err;

	speed &= ~SDIO_SPEED_BSS_MASK;
	speed |= bus_speed;
	err = SDIO_CMD52(1, 0, SDIO_CCCR_SPEED, speed, 0);
	if (err)
		return err;

    UINT16 clk = sdio_readw(SD_CTRL_BASE_ADDR, SD_CLOCK_CTRL_offset);
    clk &= ~SDHCI_CLOCK_CARD_EN;
    sdio_writew(SD_CTRL_BASE_ADDR, SD_CLOCK_CTRL_offset, clk);

    UINT16 ctrl = 0;
    //uhs mode
    ctrl = sdio_readw(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2);
    CPUartLogPrintf("%s,SDHCI_HOST_CTRL2:0x%x", __FUNCTION__, ctrl);
    ctrl |= 0x9;
    sdio_writew(SD_CTRL_BASE_ADDR, SDHCI_HOST_CTRL2, ctrl);

	return 0;
}

#ifdef QUECTEL_PROJECT_CUST
#ifdef AICWIFI_SUPPORT
void sdio_pdn_wifi(void)
{
	int rst_gpio = platform_5803_get_hw_reset_gpio();

	GpioSetLevel(rst_gpio, GPIORC_LOW);

	{
		extern int wlan_ldo_en_pin;

		if(wlan_ldo_en_pin == -1)
			return;
		GpioSetLevel(wlan_ldo_en_pin, GPIORC_LOW);
	}

}
#endif

#ifdef EVB_SDCARD_2_SDIO
extern int sdcard_clk_on(void);
extern void sdh_tx_out_clk_sel(void);
extern void sdh_set_sdcard_mode(void);
extern int sdcard_set_power(void);
extern void SdCard_Enable_Clock(UINT32 base_addr, UINT32 CLOCK);
extern UINT32 SDCARD_CTRL_BASE_ADDR;
#endif /* EVB_SDCARD_2_SDIO */
#endif

static int sdio_host_init(void)
{
    UINT32 base_addr = SD_CTRL_BASE_ADDR;
    int ret = 0;
    int fnnum = 0;
	sdio_cccr cccr;

#ifdef QUECTEL_PROJECT_CUST
#if SDIO_PHASE
    #ifdef QUECTEL_PROJECT_CUST
    int sdio_phase = sdio_read_phase_from_fs();
    if( -1 == sdio_phase )
        sdio_phase = 200;
    #else
    int sdio_phase = 225;
    #endif
#endif

#ifndef EVB_SDCARD_2_SDIO
	sdio_clk_on();
#else
    sdcard_clk_on();
#endif /* EVB_SDCARD_2_SDIO */

	sdio_mswait(100);

	AIB_MMC1_IO_Set_1_8V();



#ifndef EVB_SDCARD_2_SDIO
	sdio_set_power();

	sdio_tx_out_clk_sel();
#else
    SDCARD_CTRL_BASE_ADDR = SD1_HOST_CTRL_ADDR;
    sdh_tx_out_clk_sel();
    sdh_set_sdcard_mode();
    sdcard_set_power();
#endif /* EVB_SDCARD_2_SDIO */
#else
	sdio_clk_on();
	
	sdio_set_power();

	sdio_tx_out_clk_sel();
#endif
	//sdio 325khz clock
	if(is_sdioclk_48M)
		sdio_enable_clock(base_addr, 0x49);
	else
	#ifdef QUECTEL_PROJECT_CUST
        #ifndef EVB_SDCARD_2_SDIO
		sdio_enable_clock(base_addr, 0x140);
        #else
        SdCard_Enable_Clock(base_addr, 0x140);
        #endif /* EVB_SDCARD_2_SDIO */
	#else
		sdio_enable_clock(base_addr, 0x140);
	#endif
	
#ifdef QUECTEL_PROJECT_CUST	
	sdio_irq_enable(base_addr, 0x13F);
#else
	sdio_irq_enable(base_addr, 0x1FF);
#endif	
	sdio_errirq_enable(base_addr, 0xE0FF);

#ifndef QUECTEL_PROJECT_CUST
	if(!PlatformSDCardEnable())
#endif
	{
		INTCConfigure(INTC_SRC_MMC, INTC_IRQ, INTC_HIGH_LEVEL);
		INTCBind(INTC_SRC_MMC, sdio_isr);
		INTCEnable(INTC_SRC_MMC);
	}

#ifdef QUECTEL_PROJECT_CUST
	sdio_mswait(100);
#else
	sdio_mswait(10);
#endif
	sdio_get_capa(base_addr);

	ret = sdio_cmd_init();
	if(ret)
		goto Handle_Err;

	memset(&cccr, 0x00, sizeof(sdio_cccr));
	ret = sdio_read_cccr(&cccr);
	if(ret)
		goto Handle_Err;

	sdiocccr = cccr;

	//wide bus, set 4bits mode
	ret = sdio_endabe_widebus(&cccr);
	if(ret)
		goto Handle_Err;

	do
	{
	 	if (sd3_bus_mode)
	 	{
#if 1
	 		sdio_init_uhs_card();
			break;
#else
			if (sd3_bus_mode & SD_MODE_UHS_SDR104)
			{
				ret = sdio_set_sdr104();
				if (!ret)
				{
					//set 208M clock
					sdio_enable_clock(base_addr, 0);
					//rx tune for sdr104
					ret = sdio_rx_tune(base_addr);
					if (!ret)
						break;
				}
			}

			if (sd3_bus_mode & SD_MODE_UHS_SDR50)
			{
				ret = sdio_set_sdr50();
				if (!ret)
				{
					//set 104M clock
					sdio_enable_clock(base_addr, 1);
					//rx tune for sdr50
					ret = sdio_rx_tune(base_addr);
					if (!ret)
						break;

				}
			}

			//try sdr25
			ret = sdio_set_sdr25();
			if (!ret)
			{
				sdio_enable_clock(base_addr, 2);
				break;
			}
			else
			{
				CPUartLogPrintf("sdio try set sdr mode failed");
			}
#endif
	 	}

		//try high speed
		if(is_sdioclk_48M)
    		sdio_enable_clock(base_addr, 0);
    	else
    	{
    		//high speed if clock >= 50M
    		ret = sdio_enable_hs(&cccr);
    		if(ret)
    			goto Handle_Err;

#ifdef QUECTEL_PROJECT_CUST
	#if !defined(AICWIFI_D80)

	#if SDIO_PHASE // adjust sdio host phase

    #ifdef QUECTEL_PROJECT_CUST
        CPUartLogPrintf("host sdio phase: %d\n", sdio_phase);
        if( 0 <= sdio_phase && sdio_phase <= 255 )
        {
            //set sdr50 mode, support 100M clock
            sdio_set_sdr50();
            sdio_enable_clock(base_addr, 1);

            //rx cfg, delay code from 0 to 255
            sdio_rx_cfg(base_addr, sdio_phase);
        }
        else
        {
            // only set sdio clock
            CPUartLogPrintf("SDIO set DW 104M clock==========\n");
            sdio_enable_clock(base_addr, 1); //1->104MHz, 8->13MHz
        }
    #else
				//set sdr50 mode, support 100M clock
				sdio_set_sdr50();
				sdio_enable_clock(base_addr, 1);
				CPUartLogPrintf("host sdio phase: %d\n", sdio_phase);

				//rx cfg, delay code from 0 to 255
				sdio_rx_cfg(base_addr, sdio_phase);
    #endif
	#else
				// only set sdio clock
				CPUartLogPrintf("SDIO set DW 104M clock==========\n");
				sdio_enable_clock(base_addr, 1); //1->104MHz, 8->13MHz
	#endif /* SDIO_PHASE */

	#else /* AICWIFI_D80 */

				// try to set sdr104 mode
				ret = sdio_set_sdr104();
				if (!ret) {
					// set 208M clock
					CPUartLogPrintf("SDIO set 208M clock");
					sdio_enable_clock(base_addr, 0);
					// rx tune for sdr104
					ret = sdio_rx_tune(base_addr);
					if (ret) {
						// back to sdr50
						sdio_set_sdr50();
						CPUartLogPrintf("SDIO set 104M clock");
	                    sdio_enable_clock(base_addr, 1);
						ret = 0;
					}
				}

	#endif /* AICWIFI_D80 */
#else
	sdio_enable_clock(base_addr, 2);
#endif
    	}
		
	} while(0);
   

#if defined(AICWIFI_SUPPORT) && defined(QUECTEL_PROJECT_CUST) 
    if(sdio_read_function())
        goto Handle_Err;
#else
	if (GetWiFiType() == 1 || GetWiFiType() == 4 || GetWiFiType() == 6)
	{
		//select function
		ret = sdio_select_function(&fnnum);
		if(ret)
			goto Handle_Err;
#if SDIO_DEBUG
		CPUartLogPrintf("wifi function is %d\r\n", fnnum);
#endif
		sdiofnnum = fnnum;

		//enable function
		ret = sdio_enable_function(fnnum);
		if(ret)
			goto Handle_Err;

		//set function block size
		ret = sdio_set_block_size(fnnum, FUNCTION_BLOCKSIZE);
		if(ret)
			goto Handle_Err;

		//set function irq
		ret = sdio_set_function_irq(fnnum);
		if(ret)
			goto Handle_Err;
	}

	if(GetWiFiType() == 6)
        sdio_postpone_clk_gate();

	CPUartLogPrintf("sdio dump sdhci registers");
	sdhci_dumpregs(base_addr);

#ifdef QUECTEL_PROJECT_CUST
#if defined(SDIO_CLOCK_ADJ)
	sdio_set_clock(1);
#endif
#else
#ifdef SSVWIFI_FUNCTION
	sdio_set_clock(1);
#endif
#endif
#endif

	return 0;

Handle_Err:
	CPUartLogPrintf("sdio_host_init error");
	if(GetWiFiType() == 6)
    {
        g_wifi_wake_no_lock = 1;
    }
	return ret;
}

#ifdef QUECTEL_PROJECT_CUST
#ifdef EVB_SDCARD_2_SDIO
extern void platform_sdcard_pin(void);
#endif
#endif

/**
 *  @brief: This function initialize SDIO bus driver.
 *
 *  @return MLAN_STATUS_SUCCESS or MLAN_STATUS_FAILURE
 */

static void sdio_main_init(void * argv)
{
    OS_STATUS   status;

#ifdef QUECTEL_PROJECT_CUST
#ifndef EVB_SDCARD_2_SDIO
    SD_CTRL_BASE_ADDR = 0xD4280800;
#else
    SD_CTRL_BASE_ADDR = SD1_HOST_CTRL_ADDR;
#endif /* EVB_SDCARD_2_SDIO */
#else
	SD_CTRL_BASE_ADDR = 0xD4280800;
#endif
	CPUartLogPrintf("sdio init,SDIO:Base 0x%x",SD_CTRL_BASE_ADDR);

#ifdef	ENABLE_SDIO_ADMA
	sdioadmatl = (sdio_desc_table *)sdioadmatblnocache;
#endif

#ifndef QUECTEL_PROJECT_CUST
#if defined(SSVWIFI_FUNCTION)
#if defined(ENABLE_SDIO_ADMA) && !defined(SDIO_CLOCK_ADJ)
	set_ddr_sdh_axi_qos();
#endif
#endif
#else
#if defined(ENABLE_SDIO_ADMA) && !defined(SDIO_CLOCK_ADJ)
	set_ddr_sdh_axi_qos();
#endif
#endif

#ifdef QUECTEL_PROJECT_CUST
#ifndef EVB_SDCARD_2_SDIO
    //config wifi and sdio pinmux
//    Platform_sdio_config_pin();
	// sdio_config_nzc_mifi_dkb_pin(GPIO_PIN_83, GPIO_PIN_55, GPIO_PIN_38, GPIO_PIN_37);
//    Platform_sdio_config_pin();
	platform_sdcard_pin();
#else
    platform_sdcard_pin();
#endif /* EVB_SDCARD_2_SDIO */

    //config wifi and sdio pinmux
    wlan_ldo_en_pin = GPIO_PIN_10;
    wlan_hw_reset_pin = GPIO_PIN_10;
    host_wake_wlan_pin = GPIO_PIN_38;
    wlan_wake_host_pin = GPIO_PIN_11;
	sdio_config_nzc_mifi_dkb_pin(wlan_ldo_en_pin, wlan_hw_reset_pin, host_wake_wlan_pin, wlan_wake_host_pin);
#else
//config wifi and sdio pinmux
    //Platform_sdio_config_pin();
	sdio_config_nzc_mifi_dkb_pin(GPIO_PIN_83, GPIO_PIN_55, GPIO_PIN_38, GPIO_PIN_37);
#endif
    sdio_create_semhisr();

	int ret = sdio_host_init();
	if (!ret)
	{
		sdio_create_card_int_task();
    	RTI_LOG("sdio init OK");
//#ifdef SSVWIFI_FUNCTION
		wifi_driver_init();
//#endif
	}
	else
	{
		RTI_LOG("SDIOERR:sdio init error");
	}

    OSATimerCreate(&DeleteSdioInitTimer);
    OSATimerStart(DeleteSdioInitTimer, 10, 0, DeleteSdioInitTimerFunc, 0);
}

int sdio_init(void)
{
    if(PlatformWifiIsEnable())
    {
        OS_STATUS   status;
	    SDIOInitStack = malloc(SDIOINIT_STACK_SIZE);
	    status = OSATaskCreate(&SDIOInitRef, SDIOInitStack, SDIOINIT_STACK_SIZE, SDIOINIT_TASK_PRIORITY,
                                "sdioinit", sdio_main_init, NULL);
    }
	return 0;
}

